/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq;


import com.chidhagni.auth.db.jooq.tables.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.UserSessions;
import com.chidhagni.auth.db.jooq.tables.UserVerification;
import com.chidhagni.auth.db.jooq.tables.Users;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index IDX_PASSWORD_RESET_ACTIVE = Internal.createIndex(DSL.name("idx_password_reset_active"), UserPasswordReset.USER_PASSWORD_RESET, new OrderField[] { UserPasswordReset.USER_PASSWORD_RESET.USER_ID, UserPasswordReset.USER_PASSWORD_RESET.RESET_TOKEN }, false);
    public static final Index IDX_PASSWORD_RESET_EXPIRES_AT = Internal.createIndex(DSL.name("idx_password_reset_expires_at"), UserPasswordReset.USER_PASSWORD_RESET, new OrderField[] { UserPasswordReset.USER_PASSWORD_RESET.EXPIRES_AT }, false);
    public static final Index IDX_SESSIONS_ACTIVE = Internal.createIndex(DSL.name("idx_sessions_active"), UserSessions.USER_SESSIONS, new OrderField[] { UserSessions.USER_SESSIONS.USER_ID, UserSessions.USER_SESSIONS.SESSION_TOKEN }, false);
    public static final Index IDX_SESSIONS_REFRESH_TOKEN = Internal.createIndex(DSL.name("idx_sessions_refresh_token"), UserSessions.USER_SESSIONS, new OrderField[] { UserSessions.USER_SESSIONS.REFRESH_TOKEN }, false);
    public static final Index IDX_USERS_EMAIL = Internal.createIndex(DSL.name("idx_users_email"), Users.USERS, new OrderField[] { Users.USERS.EMAIL }, false);
    public static final Index IDX_USERS_MOBILE_NUMBER = Internal.createIndex(DSL.name("idx_users_mobile_number"), Users.USERS, new OrderField[] { Users.USERS.MOBILE_NUMBER }, false);
    public static final Index IDX_USERS_NAME = Internal.createIndex(DSL.name("idx_users_name"), Users.USERS, new OrderField[] { Users.USERS.NAME }, false);
    public static final Index IDX_USERS_ROLE = Internal.createIndex(DSL.name("idx_users_role"), Users.USERS, new OrderField[] { Users.USERS.ROLE }, false);
    public static final Index IDX_VERIFICATION_TOKEN_UNVERIFIED = Internal.createIndex(DSL.name("idx_verification_token_unverified"), UserVerification.USER_VERIFICATION, new OrderField[] { UserVerification.USER_VERIFICATION.VERIFICATION_TOKEN }, false);
}
