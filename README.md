# Spring Boot Authentication Service

A robust authentication and user management service built with Spring Boot. Supports email/password registration, email verification, password reset, JWT-based login, session management, and Google OAuth2 login.

---

## Table of Contents

- [Features](#features)
- [Getting Started](#getting-started)
- [Configuration](#configuration)
- [API Endpoints](#api-endpoints)
- [OAuth2 Authentication](#oauth2-authentication)
- [API Documentation](#api-documentation)
- [Testing](#testing)
- [Contributing](#contributing)
- [License](#license)
- [Contact](#contact)

---

## Features

- User registration with email verification
- Secure login with JWT and refresh tokens
- Password reset via email
- Google OAuth2 login
- Session management
- CORS and security best practices
- API documentation with Swagger UI

---

## Getting Started

### Prerequisites

- Java 17+
- Gradle 7+
- PostgreSQL (or update DB config for your environment)
- (Optional) Docker

### Installation

```bash
git clone https://github.com/your-org/your-repo.git
cd your-repo
./gradlew build
./gradlew bootRun
```

The application will start on [http://localhost:8080](http://localhost:8080).

---

## Configuration

Edit `src/main/resources/application.properties` to set up your environment.

**Database:**
```properties
spring.datasource.url=****************************************
spring.datasource.username=auth_db_user
spring.datasource.password=auth_db_password
```

**Email (SMTP):**
```properties
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your_app_password
```

**JWT:**
```properties
jwt.secret=your_jwt_secret
accessToken.expiry=28800000
refreshToken.expiry=86400000
```

**Google OAuth2:**
```properties
spring.security.oauth2.client.registration.google.clientId=your_google_client_id
spring.security.oauth2.client.registration.google.clientSecret=your_google_client_secret
```

**CORS:**
```properties
cors.allowed-origins=http://localhost:3000/,http://localhost:3001/
```

---

## API Endpoints

### User Authentication

- `POST /api/v1/register` — Register a new user (email verification required)
- `GET /api/v1/verify-email?token=...` — Verify user email
- `POST /api/v1/create-user` — Create user account after verification
- `POST /api/v1/login` — User login (returns JWT tokens)
- `POST /api/v1/logout` — User logout (invalidates session)
- `POST /api/v1/forgot-password` — Request password reset email
- `POST /api/v1/reset-password` — Reset password with token
- `GET /api/v1/user/{id}` — Get user info by ID

### Utility

- `POST /api/v1/resend-verification-link` — Resend verification email

---

## OAuth2 Authentication

- `GET /api/v1/oauth2/authorize/google` — Initiate Google OAuth2 login
- `GET /api/v1/oauth2/user` — Get current OAuth2 user info
- `GET /api/v1/oauth2/status` — Get OAuth2 configuration status
- `GET /api/v1/oauth2/logout` — Logout OAuth2 user

**Test pages:**  
Static HTML files for OAuth2 testing are available in `src/main/resources/static/` (e.g., `oauth2-test.html`, `google-oauth-test.html`).

---

## API Documentation

- **Swagger UI:** [http://localhost:8080/swagger.html](http://localhost:8080/swagger.html)
- **OpenAPI Spec:** (If available) `openapi.yaml` in the project root

---

## Testing

To run tests:

```bash
./gradlew test
```

---

## Contributing

Contributions are welcome! Please open issues or submit pull requests.

---

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.

---

## Contact

Maintainer: [<EMAIL>](mailto:<EMAIL>)
