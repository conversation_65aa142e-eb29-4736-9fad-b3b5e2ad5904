/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Users implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID id;
    private String email;
    private String password;
    private String name;
    private String mobileNumber;
    private Boolean isActive;
    private Boolean emailVerified;
    private Boolean accountLocked;
    private Integer failedLoginAttempts;
    private LocalDateTime lastLoginAt;
    private String socialLoginProvider;
    private String socialLoginProviderId;
    private String socialLoginProviderImageUrl;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private String role;

    public Users() {}

    public Users(Users value) {
        this.id = value.id;
        this.email = value.email;
        this.password = value.password;
        this.name = value.name;
        this.mobileNumber = value.mobileNumber;
        this.isActive = value.isActive;
        this.emailVerified = value.emailVerified;
        this.accountLocked = value.accountLocked;
        this.failedLoginAttempts = value.failedLoginAttempts;
        this.lastLoginAt = value.lastLoginAt;
        this.socialLoginProvider = value.socialLoginProvider;
        this.socialLoginProviderId = value.socialLoginProviderId;
        this.socialLoginProviderImageUrl = value.socialLoginProviderImageUrl;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.role = value.role;
    }

    public Users(
        UUID id,
        String email,
        String password,
        String name,
        String mobileNumber,
        Boolean isActive,
        Boolean emailVerified,
        Boolean accountLocked,
        Integer failedLoginAttempts,
        LocalDateTime lastLoginAt,
        String socialLoginProvider,
        String socialLoginProviderId,
        String socialLoginProviderImageUrl,
        UUID createdBy,
        UUID updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        String role
    ) {
        this.id = id;
        this.email = email;
        this.password = password;
        this.name = name;
        this.mobileNumber = mobileNumber;
        this.isActive = isActive;
        this.emailVerified = emailVerified;
        this.accountLocked = accountLocked;
        this.failedLoginAttempts = failedLoginAttempts;
        this.lastLoginAt = lastLoginAt;
        this.socialLoginProvider = socialLoginProvider;
        this.socialLoginProviderId = socialLoginProviderId;
        this.socialLoginProviderImageUrl = socialLoginProviderImageUrl;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.role = role;
    }

    /**
     * Getter for <code>users.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>users.id</code>.
     */
    public Users setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>users.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>users.email</code>.
     */
    public Users setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>users.password</code>.
     */
    public String getPassword() {
        return this.password;
    }

    /**
     * Setter for <code>users.password</code>.
     */
    public Users setPassword(String password) {
        this.password = password;
        return this;
    }

    /**
     * Getter for <code>users.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>users.name</code>.
     */
    public Users setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>users.mobile_number</code>.
     */
    public String getMobileNumber() {
        return this.mobileNumber;
    }

    /**
     * Setter for <code>users.mobile_number</code>.
     */
    public Users setMobileNumber(String mobileNumber) {
        this.mobileNumber = mobileNumber;
        return this;
    }

    /**
     * Getter for <code>users.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>users.is_active</code>.
     */
    public Users setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>users.email_verified</code>.
     */
    public Boolean getEmailVerified() {
        return this.emailVerified;
    }

    /**
     * Setter for <code>users.email_verified</code>.
     */
    public Users setEmailVerified(Boolean emailVerified) {
        this.emailVerified = emailVerified;
        return this;
    }

    /**
     * Getter for <code>users.account_locked</code>.
     */
    public Boolean getAccountLocked() {
        return this.accountLocked;
    }

    /**
     * Setter for <code>users.account_locked</code>.
     */
    public Users setAccountLocked(Boolean accountLocked) {
        this.accountLocked = accountLocked;
        return this;
    }

    /**
     * Getter for <code>users.failed_login_attempts</code>.
     */
    public Integer getFailedLoginAttempts() {
        return this.failedLoginAttempts;
    }

    /**
     * Setter for <code>users.failed_login_attempts</code>.
     */
    public Users setFailedLoginAttempts(Integer failedLoginAttempts) {
        this.failedLoginAttempts = failedLoginAttempts;
        return this;
    }

    /**
     * Getter for <code>users.last_login_at</code>.
     */
    public LocalDateTime getLastLoginAt() {
        return this.lastLoginAt;
    }

    /**
     * Setter for <code>users.last_login_at</code>.
     */
    public Users setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
        return this;
    }

    /**
     * Getter for <code>users.social_login_provider</code>.
     */
    public String getSocialLoginProvider() {
        return this.socialLoginProvider;
    }

    /**
     * Setter for <code>users.social_login_provider</code>.
     */
    public Users setSocialLoginProvider(String socialLoginProvider) {
        this.socialLoginProvider = socialLoginProvider;
        return this;
    }

    /**
     * Getter for <code>users.social_login_provider_id</code>.
     */
    public String getSocialLoginProviderId() {
        return this.socialLoginProviderId;
    }

    /**
     * Setter for <code>users.social_login_provider_id</code>.
     */
    public Users setSocialLoginProviderId(String socialLoginProviderId) {
        this.socialLoginProviderId = socialLoginProviderId;
        return this;
    }

    /**
     * Getter for <code>users.social_login_provider_image_url</code>.
     */
    public String getSocialLoginProviderImageUrl() {
        return this.socialLoginProviderImageUrl;
    }

    /**
     * Setter for <code>users.social_login_provider_image_url</code>.
     */
    public Users setSocialLoginProviderImageUrl(String socialLoginProviderImageUrl) {
        this.socialLoginProviderImageUrl = socialLoginProviderImageUrl;
        return this;
    }

    /**
     * Getter for <code>users.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>users.created_by</code>.
     */
    public Users setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>users.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>users.updated_by</code>.
     */
    public Users setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>users.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>users.created_on</code>.
     */
    public Users setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>users.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>users.updated_on</code>.
     */
    public Users setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>users.role</code>.
     */
    public String getRole() {
        return this.role;
    }

    /**
     * Setter for <code>users.role</code>.
     */
    public Users setRole(String role) {
        this.role = role;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Users other = (Users) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.email == null) {
            if (other.email != null)
                return false;
        }
        else if (!this.email.equals(other.email))
            return false;
        if (this.password == null) {
            if (other.password != null)
                return false;
        }
        else if (!this.password.equals(other.password))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.mobileNumber == null) {
            if (other.mobileNumber != null)
                return false;
        }
        else if (!this.mobileNumber.equals(other.mobileNumber))
            return false;
        if (this.isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!this.isActive.equals(other.isActive))
            return false;
        if (this.emailVerified == null) {
            if (other.emailVerified != null)
                return false;
        }
        else if (!this.emailVerified.equals(other.emailVerified))
            return false;
        if (this.accountLocked == null) {
            if (other.accountLocked != null)
                return false;
        }
        else if (!this.accountLocked.equals(other.accountLocked))
            return false;
        if (this.failedLoginAttempts == null) {
            if (other.failedLoginAttempts != null)
                return false;
        }
        else if (!this.failedLoginAttempts.equals(other.failedLoginAttempts))
            return false;
        if (this.lastLoginAt == null) {
            if (other.lastLoginAt != null)
                return false;
        }
        else if (!this.lastLoginAt.equals(other.lastLoginAt))
            return false;
        if (this.socialLoginProvider == null) {
            if (other.socialLoginProvider != null)
                return false;
        }
        else if (!this.socialLoginProvider.equals(other.socialLoginProvider))
            return false;
        if (this.socialLoginProviderId == null) {
            if (other.socialLoginProviderId != null)
                return false;
        }
        else if (!this.socialLoginProviderId.equals(other.socialLoginProviderId))
            return false;
        if (this.socialLoginProviderImageUrl == null) {
            if (other.socialLoginProviderImageUrl != null)
                return false;
        }
        else if (!this.socialLoginProviderImageUrl.equals(other.socialLoginProviderImageUrl))
            return false;
        if (this.createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!this.createdBy.equals(other.createdBy))
            return false;
        if (this.updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!this.updatedBy.equals(other.updatedBy))
            return false;
        if (this.createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!this.createdOn.equals(other.createdOn))
            return false;
        if (this.updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!this.updatedOn.equals(other.updatedOn))
            return false;
        if (this.role == null) {
            if (other.role != null)
                return false;
        }
        else if (!this.role.equals(other.role))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.password == null) ? 0 : this.password.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.mobileNumber == null) ? 0 : this.mobileNumber.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.emailVerified == null) ? 0 : this.emailVerified.hashCode());
        result = prime * result + ((this.accountLocked == null) ? 0 : this.accountLocked.hashCode());
        result = prime * result + ((this.failedLoginAttempts == null) ? 0 : this.failedLoginAttempts.hashCode());
        result = prime * result + ((this.lastLoginAt == null) ? 0 : this.lastLoginAt.hashCode());
        result = prime * result + ((this.socialLoginProvider == null) ? 0 : this.socialLoginProvider.hashCode());
        result = prime * result + ((this.socialLoginProviderId == null) ? 0 : this.socialLoginProviderId.hashCode());
        result = prime * result + ((this.socialLoginProviderImageUrl == null) ? 0 : this.socialLoginProviderImageUrl.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.role == null) ? 0 : this.role.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Users (");

        sb.append(id);
        sb.append(", ").append(email);
        sb.append(", ").append(password);
        sb.append(", ").append(name);
        sb.append(", ").append(mobileNumber);
        sb.append(", ").append(isActive);
        sb.append(", ").append(emailVerified);
        sb.append(", ").append(accountLocked);
        sb.append(", ").append(failedLoginAttempts);
        sb.append(", ").append(lastLoginAt);
        sb.append(", ").append(socialLoginProvider);
        sb.append(", ").append(socialLoginProviderId);
        sb.append(", ").append(socialLoginProviderImageUrl);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(role);

        sb.append(")");
        return sb.toString();
    }
}
