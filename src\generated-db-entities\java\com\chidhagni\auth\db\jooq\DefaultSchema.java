/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq;


import com.chidhagni.auth.db.jooq.tables.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.UserSessions;
import com.chidhagni.auth.db.jooq.tables.UserVerification;
import com.chidhagni.auth.db.jooq.tables.Users;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class DefaultSchema extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>DEFAULT_SCHEMA</code>
     */
    public static final DefaultSchema DEFAULT_SCHEMA = new DefaultSchema();

    /**
     * The table <code>user_password_reset</code>.
     */
    public final UserPasswordReset USER_PASSWORD_RESET = UserPasswordReset.USER_PASSWORD_RESET;

    /**
     * The table <code>user_sessions</code>.
     */
    public final UserSessions USER_SESSIONS = UserSessions.USER_SESSIONS;

    /**
     * The table <code>user_verification</code>.
     */
    public final UserVerification USER_VERIFICATION = UserVerification.USER_VERIFICATION;

    /**
     * The table <code>users</code>.
     */
    public final Users USERS = Users.USERS;

    /**
     * No further instances allowed
     */
    private DefaultSchema() {
        super("", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.asList(
            UserPasswordReset.USER_PASSWORD_RESET,
            UserSessions.USER_SESSIONS,
            UserVerification.USER_VERIFICATION,
            Users.USERS
        );
    }
}
