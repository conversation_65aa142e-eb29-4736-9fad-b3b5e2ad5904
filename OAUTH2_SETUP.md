# Google OAuth2 Integration Setup Guide

This guide will help you set up Google OAuth2 integration in your authentication service.

## Prerequisites

- Java 21+
- Spring Boot 3.3.4+
- PostgreSQL database
- Google Cloud Console account

## Step 1: Google Cloud Console Setup

### 1.1 Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API and Google OAuth2 API

### 1.2 Configure OAuth2 Credentials
1. Navigate to **APIs & Services** → **Credentials**
2. Click **Create Credentials** → **OAuth 2.0 Client IDs**
3. Configure:
   - **Application Type**: Web application
   - **Name**: Your app name (e.g., "Auth Service")
   - **Authorized JavaScript origins**: 
     - `http://localhost:3000` (for local development)
     - `https://yourdomain.com` (for production)
   - **Authorized redirect URIs**:
     - `http://localhost:8080/oauth2/callback/google` (local)
     - `https://api.yourdomain.com/oauth2/callback/google` (production)

### 1.3 Get Client Credentials
- **Client ID**: `your-client-id.apps.googleusercontent.com`
- **Client Secret**: `your-client-secret`

## Step 2: Update Application Properties

Update `src/main/resources/application.properties` with your Google OAuth2 credentials:

```properties
# Google OAuth2 Configuration
spring.security.oauth2.client.registration.google.clientId=your-client-id.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=your-client-secret
spring.security.oauth2.client.registration.google.redirectUri={baseUrl}/oauth2/callback/{registrationId}
spring.security.oauth2.client.registration.google.scope=email,profile

# Authorized Redirect URIs (for validation)
app.oauth2.authorizedRedirectUris=http://localhost:3000/oauth2/redirect,https://yourdomain.com/oauth2/redirect

# JWT Configuration
jwt.secret=your-jwt-secret-key-here-make-it-long-and-secure
accessToken.expiry=28800000
refreshToken.expiry=86400000
```

## Step 3: Database Schema

The OAuth2 integration uses the existing `users` table with the following OAuth2-specific fields:
- `social_login_provider` - The OAuth2 provider (e.g., "google")
- `social_login_provider_id` - The user ID from the OAuth2 provider
- `social_login_provider_image_url` - The user's profile image URL

These fields are already present in your database schema.

## Step 4: Testing the Integration

### 4.1 Start the Application
```bash
./gradlew bootRun
```

### 4.2 Test OAuth2 Flow
1. Open your browser and navigate to: `http://localhost:8080/oauth2-test.html`
2. Click "Login with Google"
3. Complete Google authentication
4. You'll be redirected back with a JWT token

### 4.3 Test API Endpoints
- **OAuth2 Authorization**: `GET /oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect`
- **User Info**: `GET /user` (requires authentication)

## Step 5: Frontend Integration

### 5.1 Basic OAuth2 Login Button
```html
<a href="/oauth2/authorize/google?redirect_uri=http://localhost:3000/oauth2/redirect">
    Login with Google
</a>
```

### 5.2 Handle OAuth2 Response
```javascript
// Check for token in URL parameters
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get('token');
const error = urlParams.get('error');

if (token) {
    // Store token and redirect to dashboard
    localStorage.setItem('accessToken', token);
    window.location.href = '/dashboard';
} else if (error) {
    // Handle error
    console.error('OAuth2 Error:', error);
    alert('Authentication failed: ' + error);
}
```

## Step 6: Production Deployment

### 6.1 Update Properties for Production
```properties
# Production OAuth2 Configuration
spring.security.oauth2.client.registration.google.clientId=your-production-client-id
spring.security.oauth2.client.registration.google.clientSecret=your-production-client-secret
spring.security.oauth2.client.registration.google.redirectUri=https://api.yourdomain.com/oauth2/callback/{registrationId}
app.oauth2.authorizedRedirectUris=https://app.yourdomain.com/oauth2/redirect
```

### 6.2 Security Considerations
- Use HTTPS in production
- Store secrets securely (environment variables)
- Implement proper error handling
- Add rate limiting
- Monitor OAuth2 flows

## Step 7: Troubleshooting

### Common Issues

1. **Invalid Redirect URI**
   - Ensure the redirect URI in Google Console exactly matches your application's callback URL
   - Check that the URI is included in the authorized redirect URIs list

2. **CORS Issues**
   - Verify CORS configuration in `SecurityConfig.java`
   - Ensure your frontend domain is allowed

3. **Token Issues**
   - Check JWT secret configuration
   - Verify token expiration settings

4. **User Creation Issues**
   - Ensure database schema supports OAuth2 user fields
   - Check that the `users` table has the required OAuth2 columns

### Debug Mode
Enable debug logging by adding to `application.properties`:
```properties
logging.level.com.chidhagni.auth.security.oauth2=DEBUG
logging.level.org.springframework.security=DEBUG
```

## Step 8: API Documentation

### OAuth2 Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/oauth2/authorize/google` | GET | Initiate Google OAuth2 login |
| `/oauth2/callback/google` | GET | OAuth2 callback (handled by Spring Security) |
| `/user` | GET | Get current user info (requires authentication) |

### Request Parameters

**OAuth2 Authorization Request:**
- `redirect_uri` (required): The URI to redirect after authentication
- `role` (optional): User role for signup
- `organisationName` (optional): Organization name for signup
- `companyType` (optional): Company type UUID for signup

### Response Format

**Successful Authentication:**
```
http://your-frontend.com/oauth2/redirect?token=eyJhbGciOiJIUzUxMiJ9...
```

**Failed Authentication:**
```
http://your-frontend.com/oauth2/redirect?error=access_denied
```

## Step 9: Additional Features

### 9.1 Multiple OAuth2 Providers
The system is designed to support multiple OAuth2 providers. To add Facebook OAuth2:

1. Add Facebook configuration to `application.properties`
2. Create `FacebookOAuth2UserInfo` class
3. Update `OAuth2UserInfoFactory`
4. Add Facebook to `AuthProvider` enum

### 9.2 Custom User Attributes
You can extend the OAuth2 user creation process by modifying the `CustomOAuth2UserService` class to handle additional user attributes or business logic.

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Spring Security OAuth2 documentation
3. Check Google OAuth2 documentation
4. Enable debug logging for detailed error information 