package com.chidhagni.auth.controller;

import com.chidhagni.auth.config.AppProperties;
import com.chidhagni.auth.exception.BadRequestException;
import com.chidhagni.auth.security.oauth2.CookieUtils;
import com.chidhagni.auth.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static com.chidhagni.auth.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME;

@RestController
@RequestMapping("/api/v1/oauth2")
@Tag(name = "OAuth2 Authentication", description = "OAuth2 authentication endpoints")
@Slf4j
public class OAuth2Controller {

    @Autowired
    private AppProperties appProperties;

    @Autowired
    private HttpCookieOAuth2AuthorizationRequestRepository httpCookieOAuth2AuthorizationRequestRepository;

    @GetMapping("/authorize/google")
    @Operation(
        summary = "Initiate Google OAuth2 Login",
        description = "Initiates Google OAuth2 authentication flow. This endpoint sets up the OAuth2 authorization request and redirects to Google."
    )
    public ResponseEntity<Map<String, Object>> authorizeGoogle(
            @Parameter(description = "The URI to redirect after successful authentication", required = true)
            @RequestParam String redirect_uri,
            @Parameter(description = "Optional role for user signup")
            @RequestParam(required = false) String role,
            @Parameter(description = "Optional organization name for signup")
            @RequestParam(required = false) String organisationName,
            @Parameter(description = "Optional company type UUID for signup")
            @RequestParam(required = false) String companyType,
            HttpServletRequest request,
            HttpServletResponse response) {
        log.info("GET /oauth2/authorize/google called with redirect_uri: {} role: {} organisationName: {} companyType: {}", redirect_uri, role, organisationName, companyType);
        
        if (!isAuthorizedRedirectUri(redirect_uri)) {
            throw new BadRequestException("Sorry! We've got an Unauthorized Redirect URI and can't proceed with the authentication");
        }

        // Store redirect URI and additional parameters in cookies
        CookieUtils.addCookie(response, REDIRECT_URI_PARAM_COOKIE_NAME, redirect_uri, 180); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
        
        if (role != null) {
            CookieUtils.addCookie(response, "oauth2_role", role, 180); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
        }
        if (organisationName != null) {
            CookieUtils.addCookie(response, "oauth2_organisation_name", organisationName, 180); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
        }
        if (companyType != null) {
            CookieUtils.addCookie(response, "oauth2_company_type", companyType, 180); // nosemgrep: java.servlets.security.audit.cookie-missing-samesite.cookie-missing-samesite
        }

        // Build the OAuth2 authorization URL
        String authUrl = UriComponentsBuilder
                .fromUriString("/oauth2/authorize/google")
                .queryParam("client_id", "google")
                .build()
                .toUriString();

        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("message", "OAuth2 authorization initiated");
        responseBody.put("authUrl", authUrl);
        responseBody.put("redirectUri", redirect_uri);
        
        return ResponseEntity.ok(responseBody);
    }

    @GetMapping("/user")
    @Operation(
        summary = "Get Current OAuth2 User Info",
        description = "Returns information about the currently authenticated OAuth2 user"
    )
    public ResponseEntity<Map<String, Object>> getUserInfo(
            @AuthenticationPrincipal OAuth2User principal) {
        log.info("GET /oauth2/user called");
        
        Map<String, Object> response = new HashMap<>();
        
        if (principal != null) {
            response.put("authenticated", true);
            response.put("name", principal.getName());
            response.put("email", principal.getAttribute("email"));
            response.put("picture", principal.getAttribute("picture"));
            response.put("attributes", principal.getAttributes());
        } else {
            response.put("authenticated", false);
            response.put("message", "No authenticated user found");
        }
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/status")
    @Operation(
        summary = "OAuth2 Status",
        description = "Returns the current OAuth2 configuration status"
    )
    public ResponseEntity<Map<String, Object>> getOAuth2Status() {
        log.info("GET /oauth2/status called");
        Map<String, Object> response = new HashMap<>();
        response.put("enabled", true);
        response.put("providers", new String[]{"google"});
        response.put("authorizedRedirectUris", appProperties.getOauth2().getAuthorizedRedirectUris());
        
        return ResponseEntity.ok(response);
    }

    @GetMapping("/logout")
    @Operation(
        summary = "OAuth2 Logout",
        description = "Logs out the current OAuth2 user and clears authentication cookies"
    )
    public ResponseEntity<Map<String, Object>> logout(
            HttpServletRequest request,
            HttpServletResponse response) {
        log.info("GET /oauth2/logout called");
        
        // Clear OAuth2 cookies
        httpCookieOAuth2AuthorizationRequestRepository.removeAuthorizationRequestCookies(request, response);
        
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("message", "Logged out successfully");
        responseBody.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.ok(responseBody);
    }

    private boolean isAuthorizedRedirectUri(String uri) {
        URI clientRedirectUri = URI.create(uri);
        return appProperties.getOauth2().getAuthorizedRedirectUris()
                .stream()
                .anyMatch(authorizedRedirectUri -> {
                    URI authorizedURI = URI.create(authorizedRedirectUri);
                    return authorizedURI.getHost().equalsIgnoreCase(clientRedirectUri.getHost())
                            && authorizedURI.getPort() == clientRedirectUri.getPort();
                });
    }
} 