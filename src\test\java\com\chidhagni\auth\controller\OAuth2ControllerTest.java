package com.chidhagni.auth.controller;

import com.chidhagni.auth.config.AppProperties;
import com.chidhagni.auth.security.oauth2.CookieUtils;
import com.chidhagni.auth.security.oauth2.HttpCookieOAuth2AuthorizationRequestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.junit.jupiter.api.Tag;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Tag("unit")
class OAuth2ControllerTest {

    @Mock
    private AppProperties appProperties;
    @Mock
    private AppProperties.OAuth2 oauth2;
    @Mock
    private HttpCookieOAuth2AuthorizationRequestRepository repo;
    @Mock
    private HttpServletRequest request;
    @Mock
    private HttpServletResponse response;

    private OAuth2Controller controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        controller = new OAuth2Controller();
        // Inject mocks via reflection
        try {
            var appPropsField = OAuth2Controller.class.getDeclaredField("appProperties");
            appPropsField.setAccessible(true);
            appPropsField.set(controller, appProperties);
            var repoField = OAuth2Controller.class.getDeclaredField("httpCookieOAuth2AuthorizationRequestRepository");
            repoField.setAccessible(true);
            repoField.set(controller, repo);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        when(appProperties.getOauth2()).thenReturn(oauth2);
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(List.of("http://localhost:3000/callback"));
    }

    @Test
    void testAuthorizeGoogle_success() {
        String redirectUri = "http://localhost:3000/callback";
        ResponseEntity<Map<String, Object>> responseEntity = controller.authorizeGoogle(
                redirectUri, "user", "org", "type", request, response);
        assertEquals(200, responseEntity.getStatusCodeValue());
        assertTrue(responseEntity.getBody().containsKey("authUrl"));
        assertEquals(redirectUri, responseEntity.getBody().get("redirectUri"));
    }

    @Test
    void testAuthorizeGoogle_unauthorizedRedirect() {
        String redirectUri = "http://malicious.com/callback";
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(List.of("http://localhost:3000/callback"));
        Exception ex = assertThrows(com.chidhagni.auth.exception.BadRequestException.class, () ->
                controller.authorizeGoogle(redirectUri, null, null, null, request, response));
        assertTrue(ex.getMessage().contains("Unauthorized Redirect URI"));
    }

    @Test
    void testGetUserInfo_authenticated() {
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("email", "<EMAIL>");
        attributes.put("picture", "http://img");
        OAuth2User user = new DefaultOAuth2User(Collections.emptyList(), attributes, "email");
        ResponseEntity<Map<String, Object>> resp = controller.getUserInfo(user);
        assertTrue((Boolean) resp.getBody().get("authenticated"));
        assertEquals("<EMAIL>", resp.getBody().get("email"));
        assertEquals("http://img", resp.getBody().get("picture"));
    }

    @Test
    void testGetUserInfo_notAuthenticated() {
        ResponseEntity<Map<String, Object>> resp = controller.getUserInfo(null);
        assertFalse((Boolean) resp.getBody().get("authenticated"));
        assertEquals("No authenticated user found", resp.getBody().get("message"));
    }

    @Test
    void testGetOAuth2Status() {
        when(oauth2.getAuthorizedRedirectUris()).thenReturn(List.of("http://localhost:3000/callback"));
        ResponseEntity<Map<String, Object>> resp = controller.getOAuth2Status();
        assertTrue((Boolean) resp.getBody().get("enabled"));
        assertArrayEquals(new String[]{"google"}, (String[]) resp.getBody().get("providers"));
        assertEquals(List.of("http://localhost:3000/callback"), resp.getBody().get("authorizedRedirectUris"));
    }

    @Test
    void testLogout() {
        doNothing().when(repo).removeAuthorizationRequestCookies(request, response);
        ResponseEntity<Map<String, Object>> resp = controller.logout(request, response);
        assertEquals("Logged out successfully", resp.getBody().get("message"));
        assertTrue(resp.getBody().containsKey("timestamp"));
    }
} 