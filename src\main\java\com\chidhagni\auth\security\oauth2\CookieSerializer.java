package com.chidhagni.auth.security.oauth2;

import jakarta.servlet.http.Cookie;
import org.springframework.util.SerializationUtils;

import java.util.Base64;

public class CookieSerializer {

    public static String serialize(Object obj) {
        return Base64.getUrlEncoder()
                .encodeToString(SerializationUtils.serialize(obj));
    }

    public static <T> T deserialize(Cookie cookie, Class<T> cls) {
        return cls.cast(SerializationUtils.deserialize(
                Base64.getUrlDecoder().decode(cookie.getValue())));
    }
} 