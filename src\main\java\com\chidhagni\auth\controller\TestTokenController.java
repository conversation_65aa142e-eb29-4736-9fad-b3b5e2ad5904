package com.chidhagni.auth.controller;

import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.daos.UsersDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserPasswordResetDao;
import com.chidhagni.auth.db.jooq.tables.pojos.UserVerification;
import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.db.jooq.tables.records.UsersRecord;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/test")
@Tag(name = "Test Token", description = "Test token endpoints")
@Slf4j
@Profile("test") // Only active in 'test' profile
@RequiredArgsConstructor
public class TestTokenController {

    private final UserVerificationDao userVerificationDao;
    private final UsersDao usersDao;
    private final UserPasswordResetDao userPasswordResetDao;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @GetMapping("/token")
    public ResponseEntity<Map<String, String>> getToken(@RequestParam String email) {
        Optional<UserVerification> verificationOpt = userVerificationDao.fetchOptionalByContactValue(email);
        Map<String, String> response = new HashMap<>();
        if (verificationOpt.isPresent()) {
            response.put("token", verificationOpt.get().getVerificationToken());
            return ResponseEntity.ok(response);
        } else {
            response.put("error", "Token not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }


    @GetMapping("/reset-token")
    public ResponseEntity<Map<String, String>> getResetToken(@RequestParam String email) {
        Map<String, String> response = new HashMap<>();
        // Find user by email
        Optional<Users> userOpt = usersDao.fetchOptionalByEmail(email);
        if (userOpt.isEmpty()) {
            response.put("error", "User not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
        // Fetch all password resets for user, filter for unused, get latest
        List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> resets = userPasswordResetDao.fetchByUserId(userOpt.get().getId());
        com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset latestUnused = resets.stream()
            .filter(r -> Boolean.FALSE.equals(r.getUsed()))
            .sorted((a, b) -> b.getCreatedAt().compareTo(a.getCreatedAt()))
            .findFirst()
            .orElse(null);
        if (latestUnused != null) {
            response.put("resetToken", latestUnused.getResetToken());
            return ResponseEntity.ok(response);
        } else {
            response.put("error", "Reset token not found");
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        }
    }
} 