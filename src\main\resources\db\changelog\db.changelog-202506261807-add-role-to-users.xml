<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-20240626-07-add-role-to-users" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT 'USER' NOT NULL;
                CREATE INDEX idx_users_role ON users(role);
                
                -- Add constraint to ensure valid roles
                ALTER TABLE users ADD CONSTRAINT chk_users_role 
                CHECK (role IN ('USER', 'ADMIN'));
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog> 