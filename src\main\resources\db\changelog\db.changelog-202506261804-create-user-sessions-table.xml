<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-20240626-04-create-user-sessions-table" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                CREATE TABLE user_sessions (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID NOT NULL,
                    session_token VARCHAR(255) NOT NULL UNIQUE,
                    refresh_token VARCHAR(255) UNIQUE,
                    expires_at TIMESTAMP NOT NULL,
                    refresh_expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    logged_out_at TIMESTAMP,
                    ip_address VARCHAR(15),
                    device_details TEXT,
                    CONSTRAINT fk_sessions_user
                        FOREIGN KEY (user_id) REFERENCES users(id)
                );
                CREATE INDEX idx_sessions_active
                ON user_sessions(user_id, session_token)
                WHERE is_active = TRUE;
                CREATE INDEX idx_sessions_refresh_token
                ON user_sessions(refresh_token);
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog> 