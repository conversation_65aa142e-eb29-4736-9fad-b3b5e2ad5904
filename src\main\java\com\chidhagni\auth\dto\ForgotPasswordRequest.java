package com.chidhagni.auth.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class ForgotPasswordRequest {
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;

    @NotBlank(message = "IP address is required")
    private String ipAddress;

    @NotBlank(message = "Device details are required")
    private String deviceDetails;
} 