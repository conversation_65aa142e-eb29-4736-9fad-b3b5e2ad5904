plugins {
    id 'java'
    id 'idea'
    id 'org.springframework.boot' version '3.2.5'
    id 'io.spring.dependency-management' version '1.1.4'
    id 'nu.studer.jooq' version '9.0'
    id 'jacoco'
    id 'org.liquibase.gradle' version '2.2.2'
    id 'org.sonarqube' version '4.3.0.3225'
}

group = 'com.chidhagni'
version = '1.0-SNAPSHOT'
sourceCompatibility = '21'
targetCompatibility = '21'

repositories {
    mavenLocal()
    mavenCentral()
}

dependencies {
    // === Spring Boot ===
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.retry:spring-retry'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus'

    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.5.0'


    compileOnly 'jakarta.servlet:jakarta.servlet-api:6.0.0'
    // JWT
    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'
    constraints {
        implementation('io.jsonwebtoken:jjwt-api:0.12.3') { because 'parserBuilder is only in 0.11.0+' }
        runtimeOnly('io.jsonwebtoken:jjwt-impl:0.12.3') { because 'parserBuilder is only in 0.11.0+' }
        runtimeOnly('io.jsonwebtoken:jjwt-jackson:0.12.3') { because 'parserBuilder is only in 0.11.0+' }
    }

    // === JOOQ ===
    implementation 'org.jooq:jooq:3.18.14'
    jooqGenerator 'org.jooq:jooq-codegen:3.18.14'
    jooqGenerator 'org.jooq:jooq-meta:3.18.14'
    jooqGenerator 'org.postgresql:postgresql:42.7.3'

    // === Database ===
    runtimeOnly 'org.postgresql:postgresql:42.7.3'
    implementation 'org.liquibase:liquibase-core:4.29.2'
    implementation 'com.zaxxer:HikariCP'

    // === Lombok ===
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // === Testing ===
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.mockito:mockito-core:4.11.0'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.11.0'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.testcontainers:junit-jupiter:1.19.7'
    testImplementation 'org.testcontainers:postgresql:1.19.7'
    testImplementation 'org.mockito:mockito-inline:5.2.0'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'io.micrometer:micrometer-core'

    liquibaseRuntime 'org.liquibase:liquibase-core:4.29.2'
    liquibaseRuntime 'org.postgresql:postgresql:42.7.3'
    liquibaseRuntime 'info.picocli:picocli:4.7.5'
}

tasks.compileJava {
    options.compilerArgs << "-Xlint:unchecked"
}

test {
    useJUnitPlatform {
        // Removed default exclusion of integration tests
        if (project.hasProperty('excludeTags')) {
            excludeTags project.property('excludeTags').split(',')
        }
        if (project.hasProperty('includeTags')) {
            includeTags project.property('includeTags').split(',')
        }
    }
    reports {
        junitXml.required.set(true)
        html.required.set(true)
    }
}

tasks.register('jooqGen') {
    dependsOn 'generateJooq'
}

liquibase {
    activities {
        main {
            changelogFile 'db/changelog/db.changelog-master.yaml'
            url '****************************************'
            username 'auth_db_user'
            password 'auth_db_password'
            searchPath 'src/main/resources'
        }
    }
    runList = 'main'
}

jooq {
    version = '3.18.14'
    edition = nu.studer.gradle.jooq.JooqEdition.OSS
    configurations {
        main {
            generateSchemaSourceOnCompilation = false
            generationTool {
                jdbc {
                    driver = 'org.postgresql.Driver'
                    url = System.getenv('JOOQ_DB_URL') ?: '****************************************'
                    user = System.getenv('JOOQ_DB_USER') ?: 'auth_db_user'
                    password = System.getenv('JOOQ_DB_PASSWORD') ?: 'auth_db_password'
                    properties {
                        property {
                            key = 'ssl'
                            value = 'false'
                        }
                    }
                }
                generator {
                    name = 'org.jooq.codegen.DefaultGenerator'
                    database {
                        name = 'org.jooq.meta.postgres.PostgresDatabase'
                        inputSchema = 'public'
                        outputSchemaToDefault = true
                        excludes = 'DATABASECHANGELOG|DATABASECHANGELOGLOCK|SHEDLOCK'
                    }
                    generate {
                        relations = false
                        deprecated = false
                        records = true
                        pojos = true
                        daos = true
                        springAnnotations = true
                        javaTimeTypes = true
                        fluentSetters = true
                        pojosEqualsAndHashCode = true
                    }
                    target {
                        packageName = 'com.chidhagni.auth.db.jooq'
                        directory = 'src/generated-db-entities/java'
                    }
                }
            }
        }
    }
}

sonarqube {
    properties {
        property "sonar.host.url", "http://***************:9000" // Replace with your SonarQube IP
        property "sonar.login", System.getenv("SONAR_TOKEN") // Uses SONAR_TOKEN from workflow
        property "sonar.projectKey", "auth-service" // Unique key, e.g., "my-auth-app"
        property "sonar.projectName", "Authentication Service" // Display name, e.g., "Auth App"
        property "sonar.java.binaries", "build/classes/java/main" // Path to compiled classes
        property "sonar.exclusions", "**/dto/**,**/generated-db-entities/**,**/pojos/**,**/records/**,**/constants/**,**/exception/**"
        property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml" // Optional: For code coverage
    }
}

apply plugin: 'jacoco'

jacocoTestReport {
    dependsOn test
    executionData.setFrom(fileTree(dir: "$buildDir/jacoco", include: "**/*.exec"))
    reports {
        html.required.set(true)
        xml.required.set(true)
        csv.required.set(false)
    }
    classDirectories.setFrom(files(project.sourceSets.main.output))
    afterEvaluate {
        classDirectories.setFrom(
            files(classDirectories.files.collect { fileTree(dir: it, exclude: [
                'com/chidhagni/auth/dto/**',
                'com/chidhagni/auth/generateddbentities/**',
                'com/chidhagni/auth/db/jooq/tables/pojos/**',
                'com/chidhagni/auth/db/jooq/tables/records/**',
                'com/chidhagni/auth/constants/**',
                'com/chidhagni/auth/exception/**'
            ]) })
        )
    }
    mustRunAfter compileJava
    mustRunAfter processResources
    dependsOn test
}

bootRun {
    systemProperties['spring.profiles.active'] = project.gradle.startParameter.systemPropertiesArgs['spring.profiles.active'] ?: 'default'
}

java {
    sourceSets {
        main {
            java {
                setSrcDirs(['src/main/java', 'src/generated-db-entities/java'])
            }
        }
    }
}

bootJar {
    archiveFileName = "auth-${project.properties['profile'] ?: 'default'}.jar"
}


