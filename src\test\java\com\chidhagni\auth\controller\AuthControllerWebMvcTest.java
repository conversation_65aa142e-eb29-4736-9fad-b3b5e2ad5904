//package com.chidhagni.auth.controller;
//
//import com.chidhagni.auth.dto.*;
//import com.chidhagni.auth.service.AuthService;
//import org.junit.jupiter.api.Tag;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mockito;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.security.test.context.support.WithMockUser;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//import com.fasterxml.jackson.databind.ObjectMapper;
//
//import java.util.UUID;
//
//@SpringBootTest
//@AutoConfigureMockMvc
//@Tag("api")
//class AuthControllerWebMvcTest {
//    @Autowired
//    private MockMvc mockMvc;
//    @MockBean
//    private AuthService authService;
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    @Test
//    void register_shouldReturnOk() throws Exception {
//        RegisterRequest request = new RegisterRequest();
//        request.setEmail("<EMAIL>");
//        request.setName("Test User");
//        request.setMobileNumber("**********");
//        Mockito.when(authService.register(any(RegisterRequest.class)))
//                .thenReturn(new RegisterResponse("Verification link sent"));
//        mockMvc.perform(post("/api/v1/register")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.message").value("Verification link sent"));
//    }
//
//    @Test
//    void verifyEmail_shouldReturnOk() throws Exception {
//        Mockito.when(authService.verifyEmail(eq("token123")))
//                .thenReturn(new VerifyEmailResponse("Email verified", "<EMAIL>", null, null));
//        mockMvc.perform(get("/api/v1/verify-email?token=token123"))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.message").value("Email verified"));
//    }
//
//    @Test
//    void createUser_shouldReturnOk() throws Exception {
//        CreateUserRequest request = new CreateUserRequest();
//        request.setEmail("<EMAIL>");
//        request.setPassword("password123");
//        request.setIpAddress("127.0.0.1");
//        request.setDeviceDetails("device");
//        Mockito.when(authService.createUser(any(CreateUserRequest.class)))
//                .thenReturn(new CreateUserResponse(UUID.randomUUID().toString(), "sessionToken", "refreshToken"));
//        mockMvc.perform(post("/api/v1/create-user")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.userId").exists())
//                .andExpect(jsonPath("$.sessionToken").value("sessionToken"));
//    }
//
//    @Test
//    void resendVerificationLink_shouldReturnOk() throws Exception {
//        ResendVerificationLinkRequest request = new ResendVerificationLinkRequest();
//        request.setEmail("<EMAIL>");
//        Mockito.when(authService.resendVerificationLink(any(ResendVerificationLinkRequest.class)))
//                .thenReturn(new GenericMessageResponse("Verification link resent"));
//        mockMvc.perform(post("/api/v1/resend-verification-link")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.message").value("Verification link resent"));
//    }
//
//    @Test
//    void forgotPassword_shouldReturnOk() throws Exception {
//        ForgotPasswordRequest request = new ForgotPasswordRequest();
//        request.setEmail("<EMAIL>");
//        request.setIpAddress("127.0.0.1");
//        request.setDeviceDetails("device");
//        Mockito.when(authService.forgotPassword(any(ForgotPasswordRequest.class)))
//                .thenReturn(new GenericMessageResponse("Password reset link sent"));
//        mockMvc.perform(post("/api/v1/forgot-password")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.message").value("Password reset link sent"));
//    }
//
//    @Test
//    void resetPassword_shouldReturnOk() throws Exception {
//        ResetPasswordRequest request = new ResetPasswordRequest();
//        request.setResetToken("resetToken");
//        request.setNewPassword("newPassword123");
//        request.setIpAddress("127.0.0.1");
//        request.setDeviceDetails("device");
//        Mockito.when(authService.resetPassword(any(ResetPasswordRequest.class)))
//                .thenReturn(new GenericMessageResponse("Password reset successful"));
//        mockMvc.perform(post("/api/v1/reset-password")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.message").value("Password reset successful"));
//    }
//
//    @Test
//    void login_shouldReturnOk() throws Exception {
//        LoginRequest request = new LoginRequest();
//        request.setEmail("<EMAIL>");
//        request.setPassword("password123");
//        request.setIpAddress("127.0.0.1");
//        request.setDeviceDetails("device");
//        request.setOverrideExistingLogins(false);
//        Mockito.when(authService.login(any(LoginRequest.class)))
//                .thenReturn(new LoginResponse(UUID.randomUUID().toString(), "sessionToken", "refreshToken", "2025-01-01T00:00:00Z", "2025-01-10T00:00:00Z"));
//        mockMvc.perform(post("/api/v1/login")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.userId").exists())
//                .andExpect(jsonPath("$.sessionToken").value("sessionToken"));
//    }
//
//    @Test
//    void logout_shouldReturnOk() throws Exception {
//        LogoutRequest request = new LogoutRequest();
//        request.setSessionToken("sessionToken");
//        request.setIpAddress("127.0.0.1");
//        request.setDeviceDetails("device");
//        Mockito.when(authService.logout(any(LogoutRequest.class)))
//                .thenReturn(new GenericMessageResponse("Logged out successfully"));
//        mockMvc.perform(post("/api/v1/logout")
//                .contentType(MediaType.APPLICATION_JSON)
//                .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.message").value("Logged out successfully"));
//    }
//
//    @Test
//    @WithMockUser(username = "<EMAIL>")
//    void getUserById_authenticated_shouldReturnOk() throws Exception {
//        UUID userId = UUID.randomUUID();
//        UserResponse userResponse = new UserResponse();
//        userResponse.setId(userId);
//        userResponse.setEmail("<EMAIL>");
//        userResponse.setName("Test User");
//        userResponse.setMobileNumber("**********");
//        userResponse.setIsActive(true);
//        userResponse.setIsEmailVerified(true);
//        userResponse.setIsAccountLocked(false);
//        Mockito.when(authService.getUserById(eq(userId))).thenReturn(userResponse);
//        mockMvc.perform(get("/api/v1/user/" + userId))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.id").value(userId.toString()))
//                .andExpect(jsonPath("$.email").value("<EMAIL>"));
//    }
//
//    // @Test
//    // void getUserById_unauthenticated_shouldReturnUnauthorized() throws Exception {
//    //     UUID userId = UUID.randomUUID();
//    //     var result = mockMvc.perform(get("/api/v1/user/" + userId))
//    //             .andReturn();
//    //     System.out.println("getUserById_unauthenticated_shouldReturnUnauthorized: status = " + result.getResponse().getStatus() + ", body = " + result.getResponse().getContentAsString());
//    //     mockMvc.perform(get("/api/v1/user/" + userId))
//    //             .andExpect(status().isForbidden());
//    // }
//}