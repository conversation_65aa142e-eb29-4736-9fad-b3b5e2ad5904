<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-20240626-03-create-user-password-reset-table" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                CREATE TABLE user_password_reset (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    user_id UUID NOT NULL,
                    reset_token VARCHAR(255) NOT NULL UNIQUE,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    used_at TIMESTAMP,
                    ip_address VARCHAR(15),
                    device_details TEXT,
                    CONSTRAINT fk_password_reset_user
                        FOREIGN KEY (user_id) REFERENCES users(id)
                );
                CREATE INDEX idx_password_reset_expires_at
                ON user_password_reset(expires_at);
                CREATE INDEX idx_password_reset_active
                ON user_password_reset(user_id, reset_token)
                WHERE used = FALSE;
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog> 