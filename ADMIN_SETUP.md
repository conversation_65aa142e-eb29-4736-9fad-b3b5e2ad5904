# Admin Functionality Setup

This document explains how to set up and use the admin functionality in the authentication service.

## Overview

The admin functionality allows authorized users with admin privileges to access administrative endpoints, such as retrieving all users in the system.

## Database Changes

A new database migration has been added to include a `role` field in the users table:

- **File**: `src/main/resources/db/changelog/db.changelog-202506261807-add-role-to-users.xml`
- **Changes**: 
  - Adds `role` column with default value 'USER'
  - Adds constraint to ensure valid roles ('USER', 'ADMIN')
  - Creates index on the role column

## Security Configuration

The security configuration has been updated to:

1. **Enable method-level security** with `@EnableMethodSecurity`
2. **Require authentication** for admin endpoints (`/api/v1/admin/**`)
3. **Use role-based authorization** with `@PreAuthorize("hasRole('ADMIN')")`

## Admin Endpoints

### Get All Users
- **URL**: `GET /api/v1/admin/users`
- **Description**: Retrieves all users in the system
- **Access**: Admin users only
- **Response**: List of users with metadata

```json
{
  "users": [
    {
      "id": "123e4567-e89b-12d3-a456-************",
      "email": "<EMAIL>",
      "name": "John Doe",
      "mobileNumber": "**********",
      "isActive": true,
      "isEmailVerified": true,
      "isAccountLocked": false,
      "createdOn": "2024-01-01T12:00:00Z",
      "updatedOn": "2024-01-01T12:00:00Z"
    }
  ],
  "totalCount": 1,
  "message": "Users retrieved successfully"
}
```

## Setting Up Admin Users

### Option 1: Using Test Endpoint (Development/Testing)
In test profile, you can create an admin user using the test endpoint:

```bash
POST /api/v1/test/create-admin
```

This creates an admin user with:
- Email: `<EMAIL>`
- Password: `admin123`
- Name: `Admin User`

### Option 2: Database Update (Production)
For production, update the user's role directly in the database:

```sql
UPDATE users SET role = 'ADMIN' WHERE email = '<EMAIL>';
```

## Role Assignment Logic

Currently, the system assigns admin role based on email address (temporary solution):

```java
if ("<EMAIL>".equals(user.getEmail())) {
    authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
}
```

**Note**: This is a temporary implementation. Once the database migration is applied and JOOQ entities are regenerated, the role assignment will be based on the `role` field in the database.

## Testing

### Integration Tests
Run the admin integration tests:

```bash
./gradlew test --tests AdminControllerIntegrationTest
```

### Manual Testing
1. Start the application in test profile
2. Create an admin user: `POST /api/v1/test/create-admin`
3. Test admin endpoint: `GET /api/v1/admin/users`

## Future Enhancements

1. **Role Management**: Add endpoints to manage user roles
2. **Admin Dashboard**: Create admin-specific UI
3. **Audit Logging**: Log admin actions
4. **Granular Permissions**: Implement more specific permissions beyond just admin role

## Security Considerations

1. **Authentication Required**: All admin endpoints require authentication
2. **Role-Based Access**: Only users with ADMIN role can access admin endpoints
3. **Method-Level Security**: Uses Spring Security's `@PreAuthorize` for fine-grained control
4. **Audit Trail**: Consider implementing logging for admin actions

## Troubleshooting

### Common Issues

1. **403 Forbidden**: User doesn't have admin role
2. **401 Unauthorized**: User not authenticated
3. **Database Errors**: Ensure database migration has been applied

### Debug Steps

1. Check if user has admin role in database
2. Verify authentication is working
3. Check application logs for security-related messages
4. Ensure database migration has been applied successfully 