/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.records;


import com.chidhagni.auth.db.jooq.tables.Users;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UsersRecord extends UpdatableRecordImpl<UsersRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>users.id</code>.
     */
    public UsersRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>users.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>users.email</code>.
     */
    public UsersRecord setEmail(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>users.email</code>.
     */
    public String getEmail() {
        return (String) get(1);
    }

    /**
     * Setter for <code>users.password</code>.
     */
    public UsersRecord setPassword(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>users.password</code>.
     */
    public String getPassword() {
        return (String) get(2);
    }

    /**
     * Setter for <code>users.name</code>.
     */
    public UsersRecord setName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>users.name</code>.
     */
    public String getName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>users.mobile_number</code>.
     */
    public UsersRecord setMobileNumber(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>users.mobile_number</code>.
     */
    public String getMobileNumber() {
        return (String) get(4);
    }

    /**
     * Setter for <code>users.is_active</code>.
     */
    public UsersRecord setIsActive(Boolean value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>users.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(5);
    }

    /**
     * Setter for <code>users.email_verified</code>.
     */
    public UsersRecord setEmailVerified(Boolean value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>users.email_verified</code>.
     */
    public Boolean getEmailVerified() {
        return (Boolean) get(6);
    }

    /**
     * Setter for <code>users.account_locked</code>.
     */
    public UsersRecord setAccountLocked(Boolean value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>users.account_locked</code>.
     */
    public Boolean getAccountLocked() {
        return (Boolean) get(7);
    }

    /**
     * Setter for <code>users.failed_login_attempts</code>.
     */
    public UsersRecord setFailedLoginAttempts(Integer value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>users.failed_login_attempts</code>.
     */
    public Integer getFailedLoginAttempts() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>users.last_login_at</code>.
     */
    public UsersRecord setLastLoginAt(LocalDateTime value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>users.last_login_at</code>.
     */
    public LocalDateTime getLastLoginAt() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>users.social_login_provider</code>.
     */
    public UsersRecord setSocialLoginProvider(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>users.social_login_provider</code>.
     */
    public String getSocialLoginProvider() {
        return (String) get(10);
    }

    /**
     * Setter for <code>users.social_login_provider_id</code>.
     */
    public UsersRecord setSocialLoginProviderId(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>users.social_login_provider_id</code>.
     */
    public String getSocialLoginProviderId() {
        return (String) get(11);
    }

    /**
     * Setter for <code>users.social_login_provider_image_url</code>.
     */
    public UsersRecord setSocialLoginProviderImageUrl(String value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>users.social_login_provider_image_url</code>.
     */
    public String getSocialLoginProviderImageUrl() {
        return (String) get(12);
    }

    /**
     * Setter for <code>users.created_by</code>.
     */
    public UsersRecord setCreatedBy(UUID value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>users.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(13);
    }

    /**
     * Setter for <code>users.updated_by</code>.
     */
    public UsersRecord setUpdatedBy(UUID value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>users.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(14);
    }

    /**
     * Setter for <code>users.created_on</code>.
     */
    public UsersRecord setCreatedOn(LocalDateTime value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>users.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(15);
    }

    /**
     * Setter for <code>users.updated_on</code>.
     */
    public UsersRecord setUpdatedOn(LocalDateTime value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>users.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(16);
    }

    /**
     * Setter for <code>users.role</code>.
     */
    public UsersRecord setRole(String value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>users.role</code>.
     */
    public String getRole() {
        return (String) get(17);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UsersRecord
     */
    public UsersRecord() {
        super(Users.USERS);
    }

    /**
     * Create a detached, initialised UsersRecord
     */
    public UsersRecord(UUID id, String email, String password, String name, String mobileNumber, Boolean isActive, Boolean emailVerified, Boolean accountLocked, Integer failedLoginAttempts, LocalDateTime lastLoginAt, String socialLoginProvider, String socialLoginProviderId, String socialLoginProviderImageUrl, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, String role) {
        super(Users.USERS);

        setId(id);
        setEmail(email);
        setPassword(password);
        setName(name);
        setMobileNumber(mobileNumber);
        setIsActive(isActive);
        setEmailVerified(emailVerified);
        setAccountLocked(accountLocked);
        setFailedLoginAttempts(failedLoginAttempts);
        setLastLoginAt(lastLoginAt);
        setSocialLoginProvider(socialLoginProvider);
        setSocialLoginProviderId(socialLoginProviderId);
        setSocialLoginProviderImageUrl(socialLoginProviderImageUrl);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setRole(role);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UsersRecord
     */
    public UsersRecord(com.chidhagni.auth.db.jooq.tables.pojos.Users value) {
        super(Users.USERS);

        if (value != null) {
            setId(value.getId());
            setEmail(value.getEmail());
            setPassword(value.getPassword());
            setName(value.getName());
            setMobileNumber(value.getMobileNumber());
            setIsActive(value.getIsActive());
            setEmailVerified(value.getEmailVerified());
            setAccountLocked(value.getAccountLocked());
            setFailedLoginAttempts(value.getFailedLoginAttempts());
            setLastLoginAt(value.getLastLoginAt());
            setSocialLoginProvider(value.getSocialLoginProvider());
            setSocialLoginProviderId(value.getSocialLoginProviderId());
            setSocialLoginProviderImageUrl(value.getSocialLoginProviderImageUrl());
            setCreatedBy(value.getCreatedBy());
            setUpdatedBy(value.getUpdatedBy());
            setCreatedOn(value.getCreatedOn());
            setUpdatedOn(value.getUpdatedOn());
            setRole(value.getRole());
            resetChangedOnNotNull();
        }
    }
}
