package com.chidhagni.auth.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class AdminUserListResponse {
    private List<UserResponse> users;
    private int totalCount;
    private String message;
    
    public AdminUserListResponse(List<UserResponse> users) {
        this.users = users;
        this.totalCount = users.size();
        this.message = "Users retrieved successfully";
    }
} 