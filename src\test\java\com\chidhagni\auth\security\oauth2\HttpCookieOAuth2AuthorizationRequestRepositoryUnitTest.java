package com.chidhagni.auth.security.oauth2;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class HttpCookieOAuth2AuthorizationRequestRepositoryUnitTest {

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    private HttpCookieOAuth2AuthorizationRequestRepository repository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        repository = new HttpCookieOAuth2AuthorizationRequestRepository();
    }

    @Test
    void testLoadAuthorizationRequest_WithValidCookie() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        String serializedRequest = CookieSerializer.serialize(authRequest);
        Cookie cookie = new Cookie(HttpCookieOAuth2AuthorizationRequestRepository.OAUTH2_AUTH_REQUEST_COOKIE_NAME, serializedRequest); // nosemgrep
        
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // When
        OAuth2AuthorizationRequest result = repository.loadAuthorizationRequest(request);

        // Then
        assertNotNull(result);
        assertEquals("google", result.getAuthorizationUri());
        assertEquals("test-client-id", result.getClientId());
        assertEquals("test-state", result.getState());
    }

    @Test
    void testLoadAuthorizationRequest_WithNoCookies() {
        // Given
        when(request.getCookies()).thenReturn(null);

        // When
        OAuth2AuthorizationRequest result = repository.loadAuthorizationRequest(request);

        // Then
        assertNull(result);
    }

    @Test
    void testLoadAuthorizationRequest_WithEmptyCookies() {
        // Given
        when(request.getCookies()).thenReturn(new Cookie[0]);

        // When
        OAuth2AuthorizationRequest result = repository.loadAuthorizationRequest(request);

        // Then
        assertNull(result);
    }

    @Test
    void testLoadAuthorizationRequest_WithDifferentCookie() {
        // Given
        Cookie cookie = new Cookie("different-cookie", "value"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // When
        OAuth2AuthorizationRequest result = repository.loadAuthorizationRequest(request);

        // Then
        assertNull(result);
    }

    @Test
    void testLoadAuthorizationRequest_WithInvalidSerializedData() {
        // Given
        Cookie cookie = new Cookie(HttpCookieOAuth2AuthorizationRequestRepository.OAUTH2_AUTH_REQUEST_COOKIE_NAME, "invalid-data"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // When & Then
        assertThrows(Exception.class, () -> repository.loadAuthorizationRequest(request));
    }

    @Test
    void testSaveAuthorizationRequest_WithValidRequest() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        when(request.getParameter(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME))
                .thenReturn("http://localhost:3000/callback");

        // When
        repository.saveAuthorizationRequest(authRequest, request, response);

        // Then
        verify(response, times(2)).addCookie(any(Cookie.class));
    }

    @Test
    void testSaveAuthorizationRequest_WithNullRequest() {
        // Given
        when(request.getCookies()).thenReturn(new Cookie[0]);

        // When
        repository.saveAuthorizationRequest(null, request, response);

        // Then
        verify(response, never()).addCookie(any(Cookie.class));
    }

    @Test
    void testSaveAuthorizationRequest_WithoutRedirectUri() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        when(request.getParameter(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME))
                .thenReturn(null);

        // When
        repository.saveAuthorizationRequest(authRequest, request, response);

        // Then
        verify(response, times(1)).addCookie(any(Cookie.class));
    }

    @Test
    void testSaveAuthorizationRequest_WithEmptyRedirectUri() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        when(request.getParameter(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME))
                .thenReturn("");

        // When
        repository.saveAuthorizationRequest(authRequest, request, response);

        // Then
        verify(response, times(1)).addCookie(any(Cookie.class));
    }

    @Test
    void testSaveAuthorizationRequest_WithWhitespaceRedirectUri() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        when(request.getParameter(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME))
                .thenReturn("   ");

        // When
        repository.saveAuthorizationRequest(authRequest, request, response);

        // Then
        verify(response, times(1)).addCookie(any(Cookie.class));
    }

    @Test
    void testRemoveAuthorizationRequest_WithExistingRequest() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        String serializedRequest = CookieSerializer.serialize(authRequest);
        Cookie cookie = new Cookie(HttpCookieOAuth2AuthorizationRequestRepository.OAUTH2_AUTH_REQUEST_COOKIE_NAME, serializedRequest); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // When
        OAuth2AuthorizationRequest result = repository.removeAuthorizationRequest(request, response);

        // Then
        assertNotNull(result);
        assertEquals("google", result.getAuthorizationUri());
        verify(response, times(1)).addCookie(any(Cookie.class));
    }

    @Test
    void testRemoveAuthorizationRequest_WithNoExistingRequest() {
        // Given
        when(request.getCookies()).thenReturn(null);

        // When
        OAuth2AuthorizationRequest result = repository.removeAuthorizationRequest(request, response);

        // Then
        assertNull(result);
        verify(response, never()).addCookie(any(Cookie.class));
    }

    @Test
    void testRemoveAuthorizationRequestCookies() {
        // Given
        Cookie cookie1 = new Cookie(HttpCookieOAuth2AuthorizationRequestRepository.OAUTH2_AUTH_REQUEST_COOKIE_NAME, "value1"); // nosemgrep
        Cookie cookie2 = new Cookie(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME, "value2"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie1, cookie2});

        // When
        repository.removeAuthorizationRequestCookies(request, response);

        // Then
        verify(response, times(2)).addCookie(any(Cookie.class));
    }

    @Test
    void testRemoveAuthorizationRequestCookies_WithNoCookies() {
        // Given
        when(request.getCookies()).thenReturn(null);

        // When
        repository.removeAuthorizationRequestCookies(request, response);

        // Then
        verify(response, never()).addCookie(any(Cookie.class));
    }

    @Test
    void testRemoveAuthorizationRequestCookies_WithDifferentCookies() {
        // Given
        Cookie cookie = new Cookie("different-cookie", "value"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});

        // When
        repository.removeAuthorizationRequestCookies(request, response);

        // Then
        verify(response, never()).addCookie(any(Cookie.class));
    }

    @Test
    void testCookieExpirationTime() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        when(request.getParameter(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME))
                .thenReturn("http://localhost:3000/callback");

        // When
        repository.saveAuthorizationRequest(authRequest, request, response);

        // Then
        verify(response, times(2)).addCookie(argThat(cookie -> 
            cookie.getMaxAge() == 600 && 
            cookie.isHttpOnly() && 
            "/".equals(cookie.getPath()) &&
            cookie.getSecure()
        ));
    }

    @Test
    void testCookieNames() {
        // Given
        OAuth2AuthorizationRequest authRequest = createSampleAuthRequest();
        when(request.getParameter(HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME))
                .thenReturn("http://localhost:3000/callback");

        // When
        repository.saveAuthorizationRequest(authRequest, request, response);

        // Then
        verify(response).addCookie(argThat(cookie -> 
            HttpCookieOAuth2AuthorizationRequestRepository.OAUTH2_AUTH_REQUEST_COOKIE_NAME.equals(cookie.getName())
        ));
        verify(response).addCookie(argThat(cookie -> 
            HttpCookieOAuth2AuthorizationRequestRepository.REDIRECT_URI_PARAM_COOKIE_NAME.equals(cookie.getName())
        ));
    }

    private OAuth2AuthorizationRequest createSampleAuthRequest() {
        Map<String, Object> additionalParameters = new HashMap<>();
        additionalParameters.put("prompt", "consent");
        
        return OAuth2AuthorizationRequest.authorizationCode()
                .authorizationUri("google")
                .clientId("test-client-id")
                .redirectUri("http://localhost:8080/oauth2/callback/google")
                .scopes(Set.of("email", "profile"))
                .state("test-state")
                .additionalParameters(additionalParameters)
                .build();
    }
} 