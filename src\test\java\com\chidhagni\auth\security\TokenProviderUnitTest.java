package com.chidhagni.auth.security;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.Tag;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@Tag("unit")
@ExtendWith(MockitoExtension.class)
class TokenProviderUnitTest {

    @InjectMocks
    private TokenProvider tokenProvider;

    private static final String STRONG_TEST_SECRET = "this-is-a-very-long-and-secure-secret-key-for-jwt-testing-purposes-only-64-chars";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(tokenProvider, "jwtSecret", STRONG_TEST_SECRET);
        ReflectionTestUtils.setField(tokenProvider, "jwtExpirationInMs", 86400000); // 24 hours
        tokenProvider.init();
    }

    @Test
    void createToken_Success() {
        UUID userId = UUID.randomUUID();
        String token = tokenProvider.createToken(userId);
        assertNotNull(token);
        assertFalse(token.isEmpty());
    }
} 