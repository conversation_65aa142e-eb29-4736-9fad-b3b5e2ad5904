/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.daos;


import com.chidhagni.auth.db.jooq.tables.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.records.UserPasswordResetRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UserPasswordResetDao extends DAOImpl<UserPasswordResetRecord, com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset, UUID> {

    /**
     * Create a new UserPasswordResetDao without any configuration
     */
    public UserPasswordResetDao() {
        super(UserPasswordReset.USER_PASSWORD_RESET, com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset.class);
    }

    /**
     * Create a new UserPasswordResetDao with an attached configuration
     */
    @Autowired
    public UserPasswordResetDao(Configuration configuration) {
        super(UserPasswordReset.USER_PASSWORD_RESET, com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchById(UUID... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset fetchOneById(UUID value) {
        return fetchOne(UserPasswordReset.USER_PASSWORD_RESET.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchOptionalById(UUID value) {
        return fetchOptional(UserPasswordReset.USER_PASSWORD_RESET.ID, value);
    }

    /**
     * Fetch records that have <code>user_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfUserId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.USER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>user_id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByUserId(UUID... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.USER_ID, values);
    }

    /**
     * Fetch records that have <code>reset_token BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfResetToken(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.RESET_TOKEN, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>reset_token IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByResetToken(String... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.RESET_TOKEN, values);
    }

    /**
     * Fetch a unique record that has <code>reset_token = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset fetchOneByResetToken(String value) {
        return fetchOne(UserPasswordReset.USER_PASSWORD_RESET.RESET_TOKEN, value);
    }

    /**
     * Fetch a unique record that has <code>reset_token = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchOptionalByResetToken(String value) {
        return fetchOptional(UserPasswordReset.USER_PASSWORD_RESET.RESET_TOKEN, value);
    }

    /**
     * Fetch records that have <code>expires_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfExpiresAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.EXPIRES_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>expires_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByExpiresAt(LocalDateTime... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.EXPIRES_AT, values);
    }

    /**
     * Fetch records that have <code>used BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfUsed(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.USED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>used IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByUsed(Boolean... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.USED, values);
    }

    /**
     * Fetch records that have <code>created_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfCreatedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.CREATED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByCreatedAt(LocalDateTime... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.CREATED_AT, values);
    }

    /**
     * Fetch records that have <code>used_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfUsedAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.USED_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>used_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByUsedAt(LocalDateTime... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.USED_AT, values);
    }

    /**
     * Fetch records that have <code>ip_address BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfIpAddress(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.IP_ADDRESS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>ip_address IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByIpAddress(String... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.IP_ADDRESS, values);
    }

    /**
     * Fetch records that have <code>device_details BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchRangeOfDeviceDetails(String lowerInclusive, String upperInclusive) {
        return fetchRange(UserPasswordReset.USER_PASSWORD_RESET.DEVICE_DETAILS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>device_details IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset> fetchByDeviceDetails(String... values) {
        return fetch(UserPasswordReset.USER_PASSWORD_RESET.DEVICE_DETAILS, values);
    }
}
