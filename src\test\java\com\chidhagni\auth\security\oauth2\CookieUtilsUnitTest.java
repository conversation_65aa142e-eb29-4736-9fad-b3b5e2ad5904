package com.chidhagni.auth.security.oauth2;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class CookieUtilsUnitTest {
    @Test
    void testGetCookie_found() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        Cookie cookie = new Cookie("test", "value"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        Optional<Cookie> result = CookieUtils.getCookie(request, "test");
        assertTrue(result.isPresent());
        assertEquals("value", result.get().getValue());
    }

    @Test
    void testGetCookie_notFound() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        when(request.getCookies()).thenReturn(new Cookie[]{});
        Optional<Cookie> result = CookieUtils.getCookie(request, "test");
        assertFalse(result.isPresent());
    }

    @Test
    void testAddCookie() {
        HttpServletResponse response = mock(HttpServletResponse.class);
        CookieUtils.addCookie(response, "name", "val", 100);
        verify(response, times(1)).addCookie(any(Cookie.class));
    }

    @Test
    void testDeleteCookie() {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        Cookie cookie = new Cookie("test", "value"); // nosemgrep
        when(request.getCookies()).thenReturn(new Cookie[]{cookie});
        CookieUtils.deleteCookie(request, response, "test");
        verify(response, times(1)).addCookie(any(Cookie.class));
    }
} 