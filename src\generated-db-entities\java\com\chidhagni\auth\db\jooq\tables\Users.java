/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables;


import com.chidhagni.auth.db.jooq.DefaultSchema;
import com.chidhagni.auth.db.jooq.Indexes;
import com.chidhagni.auth.db.jooq.Keys;
import com.chidhagni.auth.db.jooq.tables.records.UsersRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Check;
import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Users extends TableImpl<UsersRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>users</code>
     */
    public static final Users USERS = new Users();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UsersRecord> getRecordType() {
        return UsersRecord.class;
    }

    /**
     * The column <code>users.id</code>.
     */
    public final TableField<UsersRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>users.email</code>.
     */
    public final TableField<UsersRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>users.password</code>.
     */
    public final TableField<UsersRecord, String> PASSWORD = createField(DSL.name("password"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>users.name</code>.
     */
    public final TableField<UsersRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>users.mobile_number</code>.
     */
    public final TableField<UsersRecord, String> MOBILE_NUMBER = createField(DSL.name("mobile_number"), SQLDataType.VARCHAR(10).nullable(false), this, "");

    /**
     * The column <code>users.is_active</code>.
     */
    public final TableField<UsersRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("true"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>users.email_verified</code>.
     */
    public final TableField<UsersRecord, Boolean> EMAIL_VERIFIED = createField(DSL.name("email_verified"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("true"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>users.account_locked</code>.
     */
    public final TableField<UsersRecord, Boolean> ACCOUNT_LOCKED = createField(DSL.name("account_locked"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("false"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>users.failed_login_attempts</code>.
     */
    public final TableField<UsersRecord, Integer> FAILED_LOGIN_ATTEMPTS = createField(DSL.name("failed_login_attempts"), SQLDataType.INTEGER.defaultValue(DSL.field(DSL.raw("0"), SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>users.last_login_at</code>.
     */
    public final TableField<UsersRecord, LocalDateTime> LAST_LOGIN_AT = createField(DSL.name("last_login_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>users.social_login_provider</code>.
     */
    public final TableField<UsersRecord, String> SOCIAL_LOGIN_PROVIDER = createField(DSL.name("social_login_provider"), SQLDataType.VARCHAR(50), this, "");

    /**
     * The column <code>users.social_login_provider_id</code>.
     */
    public final TableField<UsersRecord, String> SOCIAL_LOGIN_PROVIDER_ID = createField(DSL.name("social_login_provider_id"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>users.social_login_provider_image_url</code>.
     */
    public final TableField<UsersRecord, String> SOCIAL_LOGIN_PROVIDER_IMAGE_URL = createField(DSL.name("social_login_provider_image_url"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>users.created_by</code>.
     */
    public final TableField<UsersRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>users.updated_by</code>.
     */
    public final TableField<UsersRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>users.created_on</code>.
     */
    public final TableField<UsersRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>users.updated_on</code>.
     */
    public final TableField<UsersRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>users.role</code>.
     */
    public final TableField<UsersRecord, String> ROLE = createField(DSL.name("role"), SQLDataType.VARCHAR(20).nullable(false).defaultValue(DSL.field(DSL.raw("'USER'::character varying"), SQLDataType.VARCHAR)), this, "");

    private Users(Name alias, Table<UsersRecord> aliased) {
        this(alias, aliased, null);
    }

    private Users(Name alias, Table<UsersRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>users</code> table reference
     */
    public Users(String alias) {
        this(DSL.name(alias), USERS);
    }

    /**
     * Create an aliased <code>users</code> table reference
     */
    public Users(Name alias) {
        this(alias, USERS);
    }

    /**
     * Create a <code>users</code> table reference
     */
    public Users() {
        this(DSL.name("users"), null);
    }

    public <O extends Record> Users(Table<O> child, ForeignKey<O, UsersRecord> key) {
        super(child, key, USERS);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.IDX_USERS_EMAIL, Indexes.IDX_USERS_MOBILE_NUMBER, Indexes.IDX_USERS_NAME, Indexes.IDX_USERS_ROLE);
    }

    @Override
    public UniqueKey<UsersRecord> getPrimaryKey() {
        return Keys.USERS_PKEY;
    }

    @Override
    public List<UniqueKey<UsersRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.USERS_EMAIL_KEY);
    }

    @Override
    public List<Check<UsersRecord>> getChecks() {
        return Arrays.asList(
            Internal.createCheck(this, DSL.name("chk_users_role"), "(((role)::text = ANY ((ARRAY['USER'::character varying, 'ADMIN'::character varying])::text[])))", true)
        );
    }

    @Override
    public Users as(String alias) {
        return new Users(DSL.name(alias), this);
    }

    @Override
    public Users as(Name alias) {
        return new Users(alias, this);
    }

    @Override
    public Users as(Table<?> alias) {
        return new Users(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public Users rename(String name) {
        return new Users(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Users rename(Name name) {
        return new Users(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public Users rename(Table<?> name) {
        return new Users(name.getQualifiedName(), null);
    }
}
