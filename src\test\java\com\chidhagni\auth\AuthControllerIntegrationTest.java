package com.chidhagni.auth;

import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.pojos.UserVerification;
import com.chidhagni.auth.dto.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Tag;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.security.test.context.support.WithMockUser;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@AutoConfigureMockMvc
@Tag("integration")
public class AuthControllerIntegrationTest extends BaseIntegrationTest {

    @Autowired
    private MockMvc mockMvc;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private UserVerificationDao userVerificationDao;

    private void verifyEmailInDb(String email) {
        Optional<UserVerification> opt = userVerificationDao.fetchOptionalByContactValue(email);
        if (opt.isPresent()) {
            UserVerification uv = opt.get();
            uv.setVerified(true);
            uv.setVerifiedAt(LocalDateTime.now());
            userVerificationDao.update(uv);
        }
    }

    @Test
    void register_and_verifyEmail_flow() throws Exception {
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setName("Integration Test");
        registerRequest.setMobileNumber("1234567890");

        // Register
        MvcResult regResult = mockMvc.perform(post("/api/v1/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk())
                .andReturn();
        String regMsg = regResult.getResponse().getContentAsString();
        assertThat(regMsg).contains("Verification link");
        verifyEmailInDb("<EMAIL>");
    }

    @Test
    void createUser_and_login_flow() throws Exception {
        // Register and verify
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setName("Integration User");
        registerRequest.setMobileNumber("1234567890");
        mockMvc.perform(post("/api/v1/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk());
        verifyEmailInDb("<EMAIL>");

        CreateUserRequest createUserRequest = new CreateUserRequest();
        createUserRequest.setEmail("<EMAIL>");
        createUserRequest.setPassword("password");
        createUserRequest.setIpAddress("127.0.0.1");
        createUserRequest.setDeviceDetails("device");

        // Create user
        mockMvc.perform(post("/api/v1/create-user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isOk());

        // Login
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("password");
        loginRequest.setIpAddress("127.0.0.1");
        loginRequest.setDeviceDetails("device");
        loginRequest.setOverrideExistingLogins(false);

        mockMvc.perform(post("/api/v1/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.userId").exists())
                .andExpect(jsonPath("$.sessionToken").exists());
    }

    @Test
    void forgotPassword_and_resetPassword_flow() throws Exception {
        // Register, verify, create user
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setName("Forgot User");
        registerRequest.setMobileNumber("1234567890");
        mockMvc.perform(post("/api/v1/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk());
        verifyEmailInDb("<EMAIL>");
        CreateUserRequest createUserRequest = new CreateUserRequest();
        createUserRequest.setEmail("<EMAIL>");
        createUserRequest.setPassword("password");
        createUserRequest.setIpAddress("127.0.0.1");
        createUserRequest.setDeviceDetails("device");
        mockMvc.perform(post("/api/v1/create-user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isOk());

        ForgotPasswordRequest forgotRequest = new ForgotPasswordRequest();
        forgotRequest.setEmail("<EMAIL>");
        forgotRequest.setIpAddress("127.0.0.1");
        forgotRequest.setDeviceDetails("device");

        mockMvc.perform(post("/api/v1/forgot-password")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(forgotRequest)))
                .andExpect(status().isOk());
        // For reset, you would need to fetch the token from DB or mock it in a real test
    }

    @Test
    void resendVerificationLink_endpoint() throws Exception {
        // Register
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setName("Resend User");
        registerRequest.setMobileNumber("1234567890");
        mockMvc.perform(post("/api/v1/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk());
        ResendVerificationLinkRequest req = new ResendVerificationLinkRequest();
        req.setEmail("<EMAIL>");
        mockMvc.perform(post("/api/v1/resend-verification-link")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(req)))
                .andExpect(status().isOk());
    }

    @Test
    @WithMockUser(username = "<EMAIL>")
    void getUserById_endpoint() throws Exception {
        // Register, verify, create user
        RegisterRequest registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setName("Get User");
        registerRequest.setMobileNumber("1234567890");
        mockMvc.perform(post("/api/v1/register")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(registerRequest)))
                .andExpect(status().isOk());
        verifyEmailInDb("<EMAIL>");
        CreateUserRequest createUserRequest = new CreateUserRequest();
        createUserRequest.setEmail("<EMAIL>");
        createUserRequest.setPassword("password");
        createUserRequest.setIpAddress("127.0.0.1");
        createUserRequest.setDeviceDetails("device");
        MvcResult result = mockMvc.perform(post("/api/v1/create-user")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createUserRequest)))
                .andExpect(status().isOk())
                .andReturn();
        String respStr = result.getResponse().getContentAsString();
        String userId = objectMapper.readTree(respStr).get("userId").asText();
        MvcResult getResult = mockMvc.perform(get("/api/v1/user/" + userId))
                .andReturn();
        System.out.println("getUserById_endpoint: response = " + getResult.getResponse().getContentAsString());
        mockMvc.perform(get("/api/v1/user/" + userId))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.email").value("<EMAIL>"));
    }
} 