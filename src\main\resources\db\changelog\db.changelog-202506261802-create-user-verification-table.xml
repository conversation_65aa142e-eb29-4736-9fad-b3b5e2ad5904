<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-20240626-02-create-user-verification-table" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                CREATE TABLE user_verification (
                    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                    contact_value VARCHAR(255) NOT NULL UNIQUE,
                    verification_token VARCHAR(255) NOT NULL UNIQUE,
                    expires_at TIMESTAMP NOT NULL,
                    verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    verified_at TIMESTAMP,
                    ip_address VARCHAR(15),
                    device_details TEXT
                );
                CREATE INDEX idx_verification_token_unverified
                ON user_verification(verification_token)
                WHERE verified = FALSE;
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog> 