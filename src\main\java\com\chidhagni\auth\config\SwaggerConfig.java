package com.chidhagni.auth.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI openAPI(@Value("${server.port:8080}") String serverPort) {
        return new OpenAPI()
                // Add API information
                .info(new Info()
                        .title("Authentication Service")
                        .description("Comprehensive authentication and authorization service with user management, email verification, and session handling")
                        .version("1.0"))
                // Add server information
                .addServersItem(new Server().url("http://localhost:" + serverPort).description("Local development server"))
                // Apply security globally (can be overridden by individual operation)
                .addSecurityItem(new SecurityRequirement().addList("bearer-key"));
    }
} 