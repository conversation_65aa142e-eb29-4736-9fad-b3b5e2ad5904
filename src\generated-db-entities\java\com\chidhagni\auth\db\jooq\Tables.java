/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq;


import com.chidhagni.auth.db.jooq.tables.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.UserSessions;
import com.chidhagni.auth.db.jooq.tables.UserVerification;
import com.chidhagni.auth.db.jooq.tables.Users;


/**
 * Convenience access to all tables in the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class Tables {

    /**
     * The table <code>user_password_reset</code>.
     */
    public static final UserPasswordReset USER_PASSWORD_RESET = UserPasswordReset.USER_PASSWORD_RESET;

    /**
     * The table <code>user_sessions</code>.
     */
    public static final UserSessions USER_SESSIONS = UserSessions.USER_SESSIONS;

    /**
     * The table <code>user_verification</code>.
     */
    public static final UserVerification USER_VERIFICATION = UserVerification.USER_VERIFICATION;

    /**
     * The table <code>users</code>.
     */
    public static final Users USERS = Users.USERS;
}
