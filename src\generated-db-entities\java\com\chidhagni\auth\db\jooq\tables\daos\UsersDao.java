/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.daos;


import com.chidhagni.auth.db.jooq.tables.Users;
import com.chidhagni.auth.db.jooq.tables.records.UsersRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
@Repository
public class UsersDao extends DAOImpl<UsersRecord, com.chidhagni.auth.db.jooq.tables.pojos.Users, UUID> {

    /**
     * Create a new UsersDao without any configuration
     */
    public UsersDao() {
        super(Users.USERS, com.chidhagni.auth.db.jooq.tables.pojos.Users.class);
    }

    /**
     * Create a new UsersDao with an attached configuration
     */
    @Autowired
    public UsersDao(Configuration configuration) {
        super(Users.USERS, com.chidhagni.auth.db.jooq.tables.pojos.Users.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.auth.db.jooq.tables.pojos.Users object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Users.USERS.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchById(UUID... values) {
        return fetch(Users.USERS.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.Users fetchOneById(UUID value) {
        return fetchOne(Users.USERS.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchOptionalById(UUID value) {
        return fetchOptional(Users.USERS.ID, value);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByEmail(String... values) {
        return fetch(Users.USERS.EMAIL, values);
    }

    /**
     * Fetch a unique record that has <code>email = value</code>
     */
    public com.chidhagni.auth.db.jooq.tables.pojos.Users fetchOneByEmail(String value) {
        return fetchOne(Users.USERS.EMAIL, value);
    }

    /**
     * Fetch a unique record that has <code>email = value</code>
     */
    public Optional<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchOptionalByEmail(String value) {
        return fetchOptional(Users.USERS.EMAIL, value);
    }

    /**
     * Fetch records that have <code>password BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfPassword(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.PASSWORD, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>password IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByPassword(String... values) {
        return fetch(Users.USERS.PASSWORD, values);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByName(String... values) {
        return fetch(Users.USERS.NAME, values);
    }

    /**
     * Fetch records that have <code>mobile_number BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfMobileNumber(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.MOBILE_NUMBER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>mobile_number IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByMobileNumber(String... values) {
        return fetch(Users.USERS.MOBILE_NUMBER, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Users.USERS.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByIsActive(Boolean... values) {
        return fetch(Users.USERS.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>email_verified BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfEmailVerified(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Users.USERS.EMAIL_VERIFIED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email_verified IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByEmailVerified(Boolean... values) {
        return fetch(Users.USERS.EMAIL_VERIFIED, values);
    }

    /**
     * Fetch records that have <code>account_locked BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfAccountLocked(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Users.USERS.ACCOUNT_LOCKED, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>account_locked IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByAccountLocked(Boolean... values) {
        return fetch(Users.USERS.ACCOUNT_LOCKED, values);
    }

    /**
     * Fetch records that have <code>failed_login_attempts BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfFailedLoginAttempts(Integer lowerInclusive, Integer upperInclusive) {
        return fetchRange(Users.USERS.FAILED_LOGIN_ATTEMPTS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>failed_login_attempts IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByFailedLoginAttempts(Integer... values) {
        return fetch(Users.USERS.FAILED_LOGIN_ATTEMPTS, values);
    }

    /**
     * Fetch records that have <code>last_login_at BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfLastLoginAt(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.LAST_LOGIN_AT, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>last_login_at IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByLastLoginAt(LocalDateTime... values) {
        return fetch(Users.USERS.LAST_LOGIN_AT, values);
    }

    /**
     * Fetch records that have <code>social_login_provider BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfSocialLoginProvider(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.SOCIAL_LOGIN_PROVIDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_login_provider IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchBySocialLoginProvider(String... values) {
        return fetch(Users.USERS.SOCIAL_LOGIN_PROVIDER, values);
    }

    /**
     * Fetch records that have <code>social_login_provider_id BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfSocialLoginProviderId(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.SOCIAL_LOGIN_PROVIDER_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_login_provider_id IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchBySocialLoginProviderId(String... values) {
        return fetch(Users.USERS.SOCIAL_LOGIN_PROVIDER_ID, values);
    }

    /**
     * Fetch records that have <code>social_login_provider_image_url BETWEEN
     * lowerInclusive AND upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfSocialLoginProviderImageUrl(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.SOCIAL_LOGIN_PROVIDER_IMAGE_URL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_login_provider_image_url IN
     * (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchBySocialLoginProviderImageUrl(String... values) {
        return fetch(Users.USERS.SOCIAL_LOGIN_PROVIDER_IMAGE_URL, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Users.USERS.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByCreatedBy(UUID... values) {
        return fetch(Users.USERS.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Users.USERS.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByUpdatedBy(UUID... values) {
        return fetch(Users.USERS.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Users.USERS.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Users.USERS.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Users.USERS.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>role BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchRangeOfRole(String lowerInclusive, String upperInclusive) {
        return fetchRange(Users.USERS.ROLE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>role IN (values)</code>
     */
    public List<com.chidhagni.auth.db.jooq.tables.pojos.Users> fetchByRole(String... values) {
        return fetch(Users.USERS.ROLE, values);
    }
}
