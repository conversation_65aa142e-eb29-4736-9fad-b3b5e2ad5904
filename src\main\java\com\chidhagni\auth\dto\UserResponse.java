package com.chidhagni.auth.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
public class UserResponse {
    private UUID id;
    private String email;
    private String name;
    private String mobileNumber;
    private Boolean isActive;
    private Boolean isEmailVerified;
    private Boolean isAccountLocked;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
} 