/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables;


import com.chidhagni.auth.db.jooq.DefaultSchema;
import com.chidhagni.auth.db.jooq.Indexes;
import com.chidhagni.auth.db.jooq.Keys;
import com.chidhagni.auth.db.jooq.tables.records.UserSessionsRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Index;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserSessions extends TableImpl<UserSessionsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>user_sessions</code>
     */
    public static final UserSessions USER_SESSIONS = new UserSessions();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<UserSessionsRecord> getRecordType() {
        return UserSessionsRecord.class;
    }

    /**
     * The column <code>user_sessions.id</code>.
     */
    public final TableField<UserSessionsRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false).defaultValue(DSL.field(DSL.raw("gen_random_uuid()"), SQLDataType.UUID)), this, "");

    /**
     * The column <code>user_sessions.user_id</code>.
     */
    public final TableField<UserSessionsRecord, UUID> USER_ID = createField(DSL.name("user_id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>user_sessions.session_token</code>.
     */
    public final TableField<UserSessionsRecord, String> SESSION_TOKEN = createField(DSL.name("session_token"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>user_sessions.refresh_token</code>.
     */
    public final TableField<UserSessionsRecord, String> REFRESH_TOKEN = createField(DSL.name("refresh_token"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>user_sessions.expires_at</code>.
     */
    public final TableField<UserSessionsRecord, LocalDateTime> EXPIRES_AT = createField(DSL.name("expires_at"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>user_sessions.refresh_expires_at</code>.
     */
    public final TableField<UserSessionsRecord, LocalDateTime> REFRESH_EXPIRES_AT = createField(DSL.name("refresh_expires_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>user_sessions.is_active</code>.
     */
    public final TableField<UserSessionsRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("true"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>user_sessions.created_at</code>.
     */
    public final TableField<UserSessionsRecord, LocalDateTime> CREATED_AT = createField(DSL.name("created_at"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>user_sessions.last_accessed_at</code>.
     */
    public final TableField<UserSessionsRecord, LocalDateTime> LAST_ACCESSED_AT = createField(DSL.name("last_accessed_at"), SQLDataType.LOCALDATETIME(6).defaultValue(DSL.field(DSL.raw("CURRENT_TIMESTAMP"), SQLDataType.LOCALDATETIME)), this, "");

    /**
     * The column <code>user_sessions.logged_out_at</code>.
     */
    public final TableField<UserSessionsRecord, LocalDateTime> LOGGED_OUT_AT = createField(DSL.name("logged_out_at"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>user_sessions.ip_address</code>.
     */
    public final TableField<UserSessionsRecord, String> IP_ADDRESS = createField(DSL.name("ip_address"), SQLDataType.VARCHAR(15), this, "");

    /**
     * The column <code>user_sessions.device_details</code>.
     */
    public final TableField<UserSessionsRecord, String> DEVICE_DETAILS = createField(DSL.name("device_details"), SQLDataType.CLOB, this, "");

    private UserSessions(Name alias, Table<UserSessionsRecord> aliased) {
        this(alias, aliased, null);
    }

    private UserSessions(Name alias, Table<UserSessionsRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>user_sessions</code> table reference
     */
    public UserSessions(String alias) {
        this(DSL.name(alias), USER_SESSIONS);
    }

    /**
     * Create an aliased <code>user_sessions</code> table reference
     */
    public UserSessions(Name alias) {
        this(alias, USER_SESSIONS);
    }

    /**
     * Create a <code>user_sessions</code> table reference
     */
    public UserSessions() {
        this(DSL.name("user_sessions"), null);
    }

    public <O extends Record> UserSessions(Table<O> child, ForeignKey<O, UserSessionsRecord> key) {
        super(child, key, USER_SESSIONS);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public List<Index> getIndexes() {
        return Arrays.asList(Indexes.IDX_SESSIONS_ACTIVE, Indexes.IDX_SESSIONS_REFRESH_TOKEN);
    }

    @Override
    public UniqueKey<UserSessionsRecord> getPrimaryKey() {
        return Keys.USER_SESSIONS_PKEY;
    }

    @Override
    public List<UniqueKey<UserSessionsRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.USER_SESSIONS_SESSION_TOKEN_KEY, Keys.USER_SESSIONS_REFRESH_TOKEN_KEY);
    }

    @Override
    public List<ForeignKey<UserSessionsRecord, ?>> getReferences() {
        return Arrays.asList(Keys.USER_SESSIONS__FK_SESSIONS_USER);
    }

    private transient Users _users;

    /**
     * Get the implicit join path to the <code>public.users</code> table.
     */
    public Users users() {
        if (_users == null)
            _users = new Users(this, Keys.USER_SESSIONS__FK_SESSIONS_USER);

        return _users;
    }

    @Override
    public UserSessions as(String alias) {
        return new UserSessions(DSL.name(alias), this);
    }

    @Override
    public UserSessions as(Name alias) {
        return new UserSessions(alias, this);
    }

    @Override
    public UserSessions as(Table<?> alias) {
        return new UserSessions(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public UserSessions rename(String name) {
        return new UserSessions(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserSessions rename(Name name) {
        return new UserSessions(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public UserSessions rename(Table<?> name) {
        return new UserSessions(name.getQualifiedName(), null);
    }
}
