/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.auth.db.jooq.tables.records;


import com.chidhagni.auth.db.jooq.tables.UserSessions;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes", "this-escape" })
public class UserSessionsRecord extends UpdatableRecordImpl<UserSessionsRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>user_sessions.id</code>.
     */
    public UserSessionsRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>user_sessions.user_id</code>.
     */
    public UserSessionsRecord setUserId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.user_id</code>.
     */
    public UUID getUserId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>user_sessions.session_token</code>.
     */
    public UserSessionsRecord setSessionToken(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.session_token</code>.
     */
    public String getSessionToken() {
        return (String) get(2);
    }

    /**
     * Setter for <code>user_sessions.refresh_token</code>.
     */
    public UserSessionsRecord setRefreshToken(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.refresh_token</code>.
     */
    public String getRefreshToken() {
        return (String) get(3);
    }

    /**
     * Setter for <code>user_sessions.expires_at</code>.
     */
    public UserSessionsRecord setExpiresAt(LocalDateTime value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.expires_at</code>.
     */
    public LocalDateTime getExpiresAt() {
        return (LocalDateTime) get(4);
    }

    /**
     * Setter for <code>user_sessions.refresh_expires_at</code>.
     */
    public UserSessionsRecord setRefreshExpiresAt(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.refresh_expires_at</code>.
     */
    public LocalDateTime getRefreshExpiresAt() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>user_sessions.is_active</code>.
     */
    public UserSessionsRecord setIsActive(Boolean value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(6);
    }

    /**
     * Setter for <code>user_sessions.created_at</code>.
     */
    public UserSessionsRecord setCreatedAt(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.created_at</code>.
     */
    public LocalDateTime getCreatedAt() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>user_sessions.last_accessed_at</code>.
     */
    public UserSessionsRecord setLastAccessedAt(LocalDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.last_accessed_at</code>.
     */
    public LocalDateTime getLastAccessedAt() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>user_sessions.logged_out_at</code>.
     */
    public UserSessionsRecord setLoggedOutAt(LocalDateTime value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.logged_out_at</code>.
     */
    public LocalDateTime getLoggedOutAt() {
        return (LocalDateTime) get(9);
    }

    /**
     * Setter for <code>user_sessions.ip_address</code>.
     */
    public UserSessionsRecord setIpAddress(String value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.ip_address</code>.
     */
    public String getIpAddress() {
        return (String) get(10);
    }

    /**
     * Setter for <code>user_sessions.device_details</code>.
     */
    public UserSessionsRecord setDeviceDetails(String value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>user_sessions.device_details</code>.
     */
    public String getDeviceDetails() {
        return (String) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached UserSessionsRecord
     */
    public UserSessionsRecord() {
        super(UserSessions.USER_SESSIONS);
    }

    /**
     * Create a detached, initialised UserSessionsRecord
     */
    public UserSessionsRecord(UUID id, UUID userId, String sessionToken, String refreshToken, LocalDateTime expiresAt, LocalDateTime refreshExpiresAt, Boolean isActive, LocalDateTime createdAt, LocalDateTime lastAccessedAt, LocalDateTime loggedOutAt, String ipAddress, String deviceDetails) {
        super(UserSessions.USER_SESSIONS);

        setId(id);
        setUserId(userId);
        setSessionToken(sessionToken);
        setRefreshToken(refreshToken);
        setExpiresAt(expiresAt);
        setRefreshExpiresAt(refreshExpiresAt);
        setIsActive(isActive);
        setCreatedAt(createdAt);
        setLastAccessedAt(lastAccessedAt);
        setLoggedOutAt(loggedOutAt);
        setIpAddress(ipAddress);
        setDeviceDetails(deviceDetails);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised UserSessionsRecord
     */
    public UserSessionsRecord(com.chidhagni.auth.db.jooq.tables.pojos.UserSessions value) {
        super(UserSessions.USER_SESSIONS);

        if (value != null) {
            setId(value.getId());
            setUserId(value.getUserId());
            setSessionToken(value.getSessionToken());
            setRefreshToken(value.getRefreshToken());
            setExpiresAt(value.getExpiresAt());
            setRefreshExpiresAt(value.getRefreshExpiresAt());
            setIsActive(value.getIsActive());
            setCreatedAt(value.getCreatedAt());
            setLastAccessedAt(value.getLastAccessedAt());
            setLoggedOutAt(value.getLoggedOutAt());
            setIpAddress(value.getIpAddress());
            setDeviceDetails(value.getDeviceDetails());
            resetChangedOnNotNull();
        }
    }
}
