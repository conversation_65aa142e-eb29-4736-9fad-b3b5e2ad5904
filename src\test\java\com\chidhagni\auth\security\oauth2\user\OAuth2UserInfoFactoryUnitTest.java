package com.chidhagni.auth.security.oauth2.user;

import com.chidhagni.auth.security.oauth2.OAuth2AuthenticationProcessingException;
import org.junit.jupiter.api.Test;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class OAuth2UserInfoFactoryUnitTest {
    @Test
    void testGetOAuth2UserInfo_google() {
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("sub", "123");
        attributes.put("name", "Test User");
        attributes.put("email", "<EMAIL>");
        attributes.put("picture", "http://img");
        OAuth2UserInfo userInfo = OAuth2UserInfoFactory.getOAuth2UserInfo("google", attributes);
        assertEquals("123", userInfo.getId());
        assertEquals("Test User", userInfo.getName());
        assertEquals("<EMAIL>", userInfo.getEmail());
        assertEquals("http://img", userInfo.getImageUrl());
    }

    @Test
    void testGetOAuth2UserInfo_unsupported() {
        Map<String, Object> attributes = new HashMap<>();
        Exception ex = assertThrows(OAuth2AuthenticationProcessingException.class, () -> {
            OAuth2UserInfoFactory.getOAuth2UserInfo("facebook", attributes);
        });
        assertTrue(ex.getMessage().contains("not supported"));
    }
} 