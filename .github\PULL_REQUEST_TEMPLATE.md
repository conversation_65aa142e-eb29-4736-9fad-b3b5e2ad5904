## The PR includes:
- [ ] Provide summary of the backend changes

## Description
Briefly describe the purpose of this change and any relevant context.

## Technical Details
Provide a summary of what was changed in the codebase, such as affected modules, packages, or layers.

## Checklist

- [ ] Application builds successfully using Gradle
- [ ] Unit and integration tests pass
- [ ] JOOQ-generated classes are committed or regenerated cleanly (if applicable)
- [ ] Liquibase scripts are verified and applied cleanly (if applicable)
- [ ] No hardcoded credentials or sensitive information in the codebase
- [ ] Logging is appropriate and secure
- [ ] SonarQube quality gate passed
- [ ] Semgrep scan completed with no high or critical issues

## Related Issue / Ticket
Add a link or reference to the relevant issue, task, or ticket.

## Additional Notes
Include any setup instructions, testing notes, or reviewer guidance.
