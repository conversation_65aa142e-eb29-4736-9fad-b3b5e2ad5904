package com.chidhagni.auth.util;

import com.chidhagni.auth.constants.AuthConstants;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.retry.annotation.Retryable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

import com.chidhagni.auth.util.EmailTemplateException;

@Component
@Slf4j
public class EmailUtil {
    private final JavaMailSender mailSender;
    private final String fromEmail;
    private final String hostUrl;

    @Autowired
    public EmailUtil(JavaMailSender mailSender, @org.springframework.beans.factory.annotation.Value("${spring.mail.from}") String fromEmail, @org.springframework.beans.factory.annotation.Value("${server.hostUrl}") String hostUrl) {
        this.mailSender = mailSender;
        this.fromEmail = fromEmail;
        this.hostUrl = hostUrl;
    }

    public void sendEmail(String to, String subject, String body) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");
            helper.setFrom(fromEmail);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(body, true); // true = isHtml
            mailSender.send(message);
            log.info("Email sent to {} with subject {}", to, subject);
        } catch (MessagingException e) {
            log.error("Failed to send email to {}: {}", to, e.getMessage());
        }
    }

    @Retryable(
        value = { RuntimeException.class },
        maxAttempts = 3,
        backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public void sendVerificationEmail(String to, String verificationLink) {
        try {
            String template = loadTemplate("templates/verification-template.html");
            String body = template.replace("${verificationLink}", verificationLink);
            sendEmail(to, "Verify your email", body);
        } catch (IOException e) {
            log.error("Failed to load verification email template: {}", e.getMessage());
            throw new EmailTemplateException("Failed to load verification email template", e);
        }
    }

    @Retryable(
        value = { RuntimeException.class },
        maxAttempts = 3,
        backoff = @Backoff(delay = 2000, multiplier = 2)
    )
    public void sendResetPasswordEmail(String to, String resetLink) {
        try {
            String template = loadTemplate("templates/reset-password.html");
            String body = template.replace("${resetLink}", resetLink);
            sendEmail(to, "Reset Your Password", body);
        } catch (IOException e) {
            log.error("Failed to load reset password email template: {}", e.getMessage());
            throw new EmailTemplateException("Failed to load reset password email template", e);
        }
    }

    @Recover
    public void recover(RuntimeException ex, String to, String link) {
        log.error("All retries failed for sending email to {}: {}", to, ex.getMessage());
        // Optionally, escalate or alert here
    }

    private String loadTemplate(String path) throws IOException {
        ClassPathResource resource = new ClassPathResource(path);
        byte[] bytes = Files.readAllBytes(resource.getFile().toPath());
        return new String(bytes, StandardCharsets.UTF_8);
    }

    public String buildVerificationLink(String token) {
        return hostUrl + String.format(AuthConstants.VERIFICATION_LINK_TEMPLATE, token);
    }

    public String buildResetLink(String token) {
        return hostUrl + String.format(AuthConstants.RESET_LINK_TEMPLATE, token);
    }
} 