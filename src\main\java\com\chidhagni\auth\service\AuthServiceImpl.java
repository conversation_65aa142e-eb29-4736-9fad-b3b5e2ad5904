package com.chidhagni.auth.service;

import com.chidhagni.auth.constants.AuthConstants;
import com.chidhagni.auth.db.jooq.Tables;
import com.chidhagni.auth.db.jooq.tables.daos.UserPasswordResetDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserSessionsDao;
import com.chidhagni.auth.db.jooq.tables.daos.UserVerificationDao;
import com.chidhagni.auth.db.jooq.tables.daos.UsersDao;
import com.chidhagni.auth.db.jooq.tables.pojos.UserPasswordReset;
import com.chidhagni.auth.db.jooq.tables.pojos.UserSessions;
import com.chidhagni.auth.db.jooq.tables.pojos.UserVerification;
import com.chidhagni.auth.db.jooq.tables.pojos.Users;
import com.chidhagni.auth.db.jooq.tables.records.UserPasswordResetRecord;
import com.chidhagni.auth.db.jooq.tables.records.UserSessionsRecord;
import com.chidhagni.auth.db.jooq.tables.records.UserVerificationRecord;
import com.chidhagni.auth.db.jooq.tables.records.UsersRecord;
import com.chidhagni.auth.dto.*;
import com.chidhagni.auth.exception.ApiException;
import com.chidhagni.auth.security.TokenProvider;
import com.chidhagni.auth.util.EmailUtil;
import com.chidhagni.auth.util.TokenUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.springframework.http.HttpStatus;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class AuthServiceImpl implements AuthService {
    private final UserVerificationDao userVerificationDao;
    private final UsersDao usersDao;
    private final UserSessionsDao userSessionsDao;
    private final UserPasswordResetDao userPasswordResetDao;
    private final DSLContext dsl;
    private final EmailUtil emailUtil;
    private final ObjectMapper objectMapper;
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    private final TokenProvider tokenProvider;
    private final MeterRegistry meterRegistry;

    @Override
    public RegisterResponse register(RegisterRequest request) {
        log.info("Registering user with email: {}", request.getEmail());
        meterRegistry.counter("auth_register_total").increment();
        try {
            return userVerificationDao.fetchOptionalByContactValue(request.getEmail())
                    .map(existing -> handleExistingVerification(existing, request))
                    .orElseGet(() -> createNewVerification(request));
        } catch (RuntimeException ex) {
            log.error("Error during register: {}", ex.getMessage(), ex);
            meterRegistry.counter("auth_register_error_total").increment();
            throw new ApiException("Internal server error during registration", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private RegisterResponse handleExistingVerification(UserVerification existing, RegisterRequest request) {
        if (Boolean.TRUE.equals(existing.getVerified())) {
            log.info("Email {} already verified", request.getEmail());
            return new RegisterResponse("Email already verified");
        }
        if (existing.getExpiresAt().isAfter(LocalDateTime.now(ZoneOffset.UTC))) {
            log.info("Verification link already sent to {} and not expired", request.getEmail());
            return new RegisterResponse("Verification link already sent. Please check your email.");
        }
        // Token expired, generate new token and update record
        log.info("Verification token expired for {}. Generating new token.", request.getEmail());
        updateVerificationWithNewToken(existing, request);
        log.info("Verification", existing);
        sendVerificationEmail(request.getEmail(), existing.getVerificationToken());
        return new RegisterResponse("Verification link resent");
    }

    private RegisterResponse createNewVerification(RegisterRequest request) {
        String token = TokenUtil.generateToken(AuthConstants.VERIFICATION_TOKEN_BYTES);
        LocalDateTime expiresAt = getVerificationExpiryTime();
        log.info("Creating new verification for email: {}", request.getEmail());
        UserVerificationRecord record = createVerificationRecord(request, token, expiresAt);
        userVerificationDao.insert(record.into(UserVerification.class));
        sendVerificationEmail(request.getEmail(), token);
        return new RegisterResponse("Verification link sent");
    }

    private void updateVerificationWithNewToken(UserVerification existing, RegisterRequest request) {
        String token = TokenUtil.generateToken(AuthConstants.VERIFICATION_TOKEN_BYTES);
        LocalDateTime expiresAt = getVerificationExpiryTime();
        
        existing.setName(request.getName());
        existing.setMobileNumber(request.getMobileNumber());
        existing.setVerificationToken(token);
        existing.setExpiresAt(expiresAt);
        existing.setCreatedAt(LocalDateTime.now(ZoneOffset.UTC));
        userVerificationDao.update(existing);
    }

    private UserVerificationRecord createVerificationRecord(RegisterRequest request, String token, LocalDateTime expiresAt) {
        UserVerificationRecord record = new UserVerificationRecord();
        record.setId(UUID.randomUUID());
        record.setContactValue(request.getEmail());
        record.setVerificationToken(token);
        record.setExpiresAt(expiresAt);
        record.setName(request.getName());
        record.setMobileNumber(request.getMobileNumber());
        record.setVerified(false);
        record.setCreatedAt(LocalDateTime.now(ZoneOffset.UTC));
        record.setIpAddress(null);
        record.setDeviceDetails(null);
        return record;
    }

    @Override
    public VerifyEmailResponse verifyEmail(String token) {
        log.info("Verifying email with token: {}", token);
        UserVerification verification = userVerificationDao.fetchOptionalByVerificationToken(token)
                .orElseThrow(() -> {
                    log.warn("Invalid or expired verification token: {}", token);
                    return new ApiException("Invalid or expired token", HttpStatus.NOT_FOUND);
                });
        validateVerificationToken(verification);
        // Mark as verified
        verification.setVerified(true);
        verification.setVerifiedAt(LocalDateTime.now(ZoneOffset.UTC));
        userVerificationDao.update(verification);
        log.info("Email {} verified successfully", verification.getContactValue());
        return new VerifyEmailResponse("Email verified successfully", verification.getContactValue(), null, null);
    }

    private void validateVerificationToken(UserVerification verification) {
        if (Boolean.TRUE.equals(verification.getVerified())) {
            log.warn("Attempt to verify already verified email: {}", verification.getContactValue());
            throw new ApiException("Email already verified", HttpStatus.CONFLICT);
        }
        if (verification.getExpiresAt().isBefore(LocalDateTime.now(ZoneOffset.UTC))) {
            log.warn("Verification token expired for email: {}", verification.getContactValue());
            throw new ApiException("Token expired", HttpStatus.BAD_REQUEST);
        }
    }

    @Override
    public CreateUserResponse createUser(CreateUserRequest request) {
        log.info("Creating user for email: {}", request.getEmail());
        meterRegistry.counter("auth_create_user_total").increment();
        UserVerification verification = userVerificationDao.fetchOptionalByContactValue(request.getEmail())
                .filter(v -> Boolean.TRUE.equals(v.getVerified()))
                .orElseThrow(() -> {
                    log.warn("Attempt to create user for unverified email: {}", request.getEmail());
                    return new ApiException("Email not verified", HttpStatus.BAD_REQUEST);
                });
        if (!usersDao.fetchByEmail(request.getEmail()).isEmpty()) {
            log.warn("User already exists for email: {}", request.getEmail());
            throw new ApiException("User already exists", HttpStatus.CONFLICT);
        }
        // Create user
        UsersRecord user = createUserRecord(request, verification);
        usersDao.insert(user.into(Users.class));
        // Create session
        UserSessionsRecord session = createUserSession(user.getId(), request.getIpAddress(), request.getDeviceDetails());
        userSessionsDao.insert(session.into(UserSessions.class));
        log.info("User created and session started for email: {}", request.getEmail());
        return new CreateUserResponse(user.getId().toString(), session.getSessionToken(), session.getRefreshToken());
    }

    private UsersRecord createUserRecord(CreateUserRequest request, UserVerification verification) {
        UsersRecord user = new UsersRecord();
        user.setId(UUID.randomUUID());
        user.setEmail(request.getEmail());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setName(verification.getName());
        user.setMobileNumber(verification.getMobileNumber());
        user.setIsActive(true);
        user.setEmailVerified(true);
        user.setAccountLocked(false);
        user.setFailedLoginAttempts(0);
        user.setCreatedOn(LocalDateTime.now(ZoneOffset.UTC));
        user.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        return user;
    }

    @Override
    public GenericMessageResponse resendVerificationLink(ResendVerificationLinkRequest request) {
        log.info("Resending verification link to email: {}", request.getEmail());
        UserVerification verification = userVerificationDao.fetchOptionalByContactValue(request.getEmail())
                .filter(v -> !Boolean.TRUE.equals(v.getVerified()))
                .orElseThrow(() -> {
                    log.warn("No unverified record found for email: {}", request.getEmail());
                    return new ApiException("No unverified record found", HttpStatus.NOT_FOUND);
                });
        // Generate new token
        String token = TokenUtil.generateToken(AuthConstants.VERIFICATION_TOKEN_BYTES);
        LocalDateTime expiresAt = getVerificationExpiryTime();
        verification.setVerificationToken(token);
        verification.setExpiresAt(expiresAt);
        userVerificationDao.update(verification);
        sendVerificationEmail(request.getEmail(), token);
        log.info("Verification link resent to email: {}", request.getEmail());
        return new GenericMessageResponse("Verification link resent");
    }

    @Override
    public GenericMessageResponse forgotPassword(ForgotPasswordRequest request) {
        log.info("Forgot password for email: {}", request.getEmail());
        meterRegistry.counter("auth_forgot_password_total").increment();
        try {
            Users user = usersDao.fetchByEmail(request.getEmail()).stream().findFirst()
                    .orElseThrow(() -> {
                        log.warn("User not found for forgot password: {}", request.getEmail());
                        return new ApiException("User not found", HttpStatus.NOT_FOUND);
                    });
            // Invalidate all previous, active reset tokens for this user
            invalidateExistingResetTokens(user.getId());
            // Generate new reset token
            String resetToken = TokenUtil.generateToken(AuthConstants.RESET_TOKEN_BYTES);
            LocalDateTime expiresAt = getResetTokenExpiryTime();
            UserPasswordResetRecord reset = createPasswordResetRecord(user.getId(), resetToken, expiresAt, request);
            userPasswordResetDao.insert(reset.into(UserPasswordReset.class));
            sendResetPasswordEmail(request.getEmail(), resetToken);
            log.info("Password reset link sent to email: {}", request.getEmail());
            return new GenericMessageResponse("Password reset link sent to your email");
        } catch (RuntimeException ex) {
            log.error("Error during forgotPassword: {}", ex.getMessage(), ex);
            meterRegistry.counter("auth_forgot_password_error_total").increment();
            throw new ApiException("Internal server error during forgot password", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void invalidateExistingResetTokens(UUID userId) {
        dsl.update(Tables.USER_PASSWORD_RESET)
                .set(Tables.USER_PASSWORD_RESET.USED, true)
                .set(Tables.USER_PASSWORD_RESET.USED_AT, LocalDateTime.now(ZoneOffset.UTC))
                .where(Tables.USER_PASSWORD_RESET.USER_ID.eq(userId)
                        .and(Tables.USER_PASSWORD_RESET.USED.isFalse())
                        .and(Tables.USER_PASSWORD_RESET.EXPIRES_AT.gt(LocalDateTime.now(ZoneOffset.UTC))))
                .execute();
    }

    private UserPasswordResetRecord createPasswordResetRecord(UUID userId, String resetToken, LocalDateTime expiresAt, ForgotPasswordRequest request) {
        UserPasswordResetRecord reset = new UserPasswordResetRecord();
        reset.setId(UUID.randomUUID());
        reset.setUserId(userId);
        reset.setResetToken(resetToken);
        reset.setExpiresAt(expiresAt);
        reset.setUsed(false);
        reset.setCreatedAt(LocalDateTime.now(ZoneOffset.UTC));
        reset.setIpAddress(request.getIpAddress());
        reset.setDeviceDetails(request.getDeviceDetails());
        return reset;
    }

    @Override
    public GenericMessageResponse resetPassword(ResetPasswordRequest request) {
        log.info("Resetting password for token: {}", request.getResetToken());
        meterRegistry.counter("auth_reset_password_total").increment();
        Optional<UserPasswordReset> resetOpt = userPasswordResetDao.fetchOptionalByResetToken(request.getResetToken());
        if (resetOpt.isEmpty()) {
            log.warn("Invalid reset token: {}", request.getResetToken());
            throw new ApiException("Invalid reset token", HttpStatus.BAD_REQUEST);
        }
        UserPasswordReset reset = resetOpt.get();
        if (Boolean.TRUE.equals(reset.getUsed())) {
            log.warn("Reset token already used: {}", request.getResetToken());
            throw new ApiException("Reset token already used", HttpStatus.BAD_REQUEST);
        }
        if (reset.getExpiresAt().isBefore(LocalDateTime.now(ZoneOffset.UTC))) {
            log.warn("Reset token expired: {}", request.getResetToken());
            throw new ApiException("Reset token expired", HttpStatus.BAD_REQUEST);
        }
        // Password strength validation (min 8 chars)
        if (request.getNewPassword() == null || request.getNewPassword().length() < 8) {
            log.warn("Password too short for reset: {}", request.getNewPassword());
            throw new ApiException("Password must be at least 8 characters", HttpStatus.BAD_REQUEST);
        }
        // Update password
        Users user = usersDao.fetchOptionalById(reset.getUserId())
                .orElseThrow(() -> {
                    log.warn("User not found for reset password token: {}", request.getResetToken());
                    return new ApiException("User not found", HttpStatus.NOT_FOUND);
                });
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        usersDao.update(user);
        // Mark token as used
        reset.setUsed(true);
        reset.setUsedAt(LocalDateTime.now(ZoneOffset.UTC));
        userPasswordResetDao.update(reset);
        log.info("Password reset successful for user: {}", user.getEmail());
        return new GenericMessageResponse("Password reset successful");
    }

    @Override
    public LoginResponse login(LoginRequest request) {
        log.info("Login attempt for email: {}", request.getEmail());
        try {
            Users user = usersDao.fetchByEmail(request.getEmail()).stream().findFirst()
                    .orElseThrow(() -> {
                        log.warn("Invalid credentials for email: {}", request.getEmail());
                        return new ApiException("Invalid credentials", HttpStatus.UNAUTHORIZED);
                    });
            if (Boolean.TRUE.equals(user.getAccountLocked())) {
                log.warn("Account is locked for email: {}", request.getEmail());
                throw new ApiException("Account is locked", HttpStatus.FORBIDDEN);
            }

            if (request.getPassword() != null) {
                // Normal login flow
                if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
                    handleFailedLogin(user);
                    log.warn("Invalid credentials for email: {} (wrong password)", request.getEmail());
                    throw new ApiException("Invalid credentials", HttpStatus.UNAUTHORIZED);
                }
                handleSuccessfulLogin(user);
            } else {
                // OAuth2 login flow (no password)
                log.info("OAuth2 login for email: {}", request.getEmail());
                handleSuccessfulLogin(user); // Optionally update last login, etc.
            }

            if (request.isOverrideExistingLogins()) {
                deactivateExistingSessions(user.getId());
            }
            // Generate JWT as sessionToken
            String jwt = tokenProvider.createToken(user.getId());
            String refreshToken = tokenProvider.createRefreshToken(user.getId());
            String expiresAt = tokenProvider.getTokenExpiry(jwt);
            String refreshExpiresAt = tokenProvider.getTokenExpiry(refreshToken);
            log.info("User logged in successfully: {} (JWT issued)", request.getEmail());
            meterRegistry.counter("auth_login_success_total").increment();
            return new LoginResponse(
                user.getId().toString(),
                jwt, // JWT as sessionToken
                refreshToken,
                expiresAt,
                refreshExpiresAt
            );
        } catch (RuntimeException ex) {
            log.error("Error during login: {}", ex.getMessage(), ex);
            meterRegistry.counter("auth_login_failure_total").increment();
            throw new ApiException("Internal server error during login", HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private void handleFailedLogin(Users user) {
        user.setFailedLoginAttempts(user.getFailedLoginAttempts() + 1);
        if (user.getFailedLoginAttempts() >= AuthConstants.MAX_FAILED_LOGIN_ATTEMPTS) {
            user.setAccountLocked(true);
        }
        usersDao.update(user);
    }

    private void handleSuccessfulLogin(Users user) {
        user.setFailedLoginAttempts(0);
        user.setLastLoginAt(LocalDateTime.now(ZoneOffset.UTC));
        usersDao.update(user);
    }

    private void deactivateExistingSessions(UUID userId) {
        userSessionsDao.fetchByUserId(userId).forEach(session -> {
            session.setIsActive(false);
            userSessionsDao.update(session);
        });
    }

    @Override
    public GenericMessageResponse logout(LogoutRequest request) {
        log.info("User logout attempt for session token: {}", request.getSessionToken());
        UserSessions session = userSessionsDao.fetchBySessionToken(request.getSessionToken()).stream().findFirst()
                .filter(s -> Boolean.TRUE.equals(s.getIsActive()))
                .orElseThrow(() -> {
                    log.warn("Session not found or already inactive for token: {}", request.getSessionToken());
                    return new ApiException("Session not found or already inactive", HttpStatus.NOT_FOUND);
                });
        session.setIsActive(false);
        session.setLoggedOutAt(LocalDateTime.now(ZoneOffset.UTC));
        userSessionsDao.update(session);
        log.info("User logged out successfully for session token: {}", request.getSessionToken());
        return new GenericMessageResponse("Logged out successfully");
    }

    @Override
    public UserResponse getUserById(UUID userId) {
        log.info("Fetching user by ID: {}", userId);
        Users user = usersDao.fetchOptionalById(userId)
                .orElseThrow(() -> {
                    log.warn("User not found for ID: {}", userId);
                    return new ApiException("User not found", HttpStatus.NOT_FOUND);
                });
        return createUserResponse(user);
    }

    @Override
    public AdminUserListResponse getAllUsers() {
        log.info("Admin requesting all users");
        List<Users> users = usersDao.findAll();
        List<UserResponse> userResponses = users.stream()
                .map(this::createUserResponse)
                .toList();
        log.info("Retrieved {} users for admin", userResponses.size());
        return new AdminUserListResponse(userResponses);
    }

    private UserResponse createUserResponse(Users user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setEmail(user.getEmail());
        response.setName(user.getName());
        response.setMobileNumber(user.getMobileNumber());
        response.setIsActive(user.getIsActive());
        response.setIsEmailVerified(user.getEmailVerified());
        response.setIsAccountLocked(user.getAccountLocked());
        response.setCreatedOn(user.getCreatedOn());
        response.setUpdatedOn(user.getUpdatedOn());
        return response;
    }

    @Override
    public Optional<Users> findUserByEmail(String email) {
        log.debug("Finding user by email: {}", email);
        return usersDao.fetchByEmail(email).stream().findFirst();
    }

    @Override
    public Users createOAuth2User(Users user) {
        log.info("Creating OAuth2 user: {}", user.getEmail());
        user.setCreatedOn(LocalDateTime.now(ZoneOffset.UTC));
        user.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        usersDao.insert(user);
        return user;
    }

    @Override
    public Users updateOAuth2User(Users user) {
        log.info("Updating OAuth2 user: {}", user.getEmail());
        user.setUpdatedOn(LocalDateTime.now(ZoneOffset.UTC));
        usersDao.update(user);
        return user;
    }

    // Utility methods
    private LocalDateTime getVerificationExpiryTime() {
        return LocalDateTime.now(ZoneOffset.UTC).plusMinutes(AuthConstants.VERIFICATION_TOKEN_EXPIRY_MINUTES);
    }

    private LocalDateTime getResetTokenExpiryTime() {
        return LocalDateTime.now(ZoneOffset.UTC).plusMinutes(AuthConstants.RESET_TOKEN_EXPIRY_MINUTES);
    }

    private UserSessionsRecord createUserSession(UUID userId, String ipAddress, String deviceDetails) {
        String sessionToken = TokenUtil.generateToken(AuthConstants.SESSION_TOKEN_BYTES);
        String refreshToken = TokenUtil.generateToken(AuthConstants.REFRESH_TOKEN_BYTES);
        LocalDateTime expiresAt = LocalDateTime.now(ZoneOffset.UTC).plusHours(AuthConstants.SESSION_EXPIRY_HOURS);
        LocalDateTime refreshExpiresAt = LocalDateTime.now(ZoneOffset.UTC).plusDays(AuthConstants.REFRESH_TOKEN_EXPIRY_DAYS);
        
        UserSessionsRecord session = new UserSessionsRecord();
        session.setId(UUID.randomUUID());
        session.setUserId(userId);
        session.setSessionToken(sessionToken);
        session.setRefreshToken(refreshToken);
        session.setExpiresAt(expiresAt);
        session.setRefreshExpiresAt(refreshExpiresAt);
        session.setIsActive(true);
        session.setCreatedAt(LocalDateTime.now(ZoneOffset.UTC));
        session.setLastAccessedAt(LocalDateTime.now(ZoneOffset.UTC));
        session.setIpAddress(ipAddress);
        session.setDeviceDetails(deviceDetails);
        return session;
    }

    private void sendVerificationEmail(String email, String token) {
        String link = emailUtil.buildVerificationLink(token);
        emailUtil.sendVerificationEmail(email, link);
    }

    private void sendResetPasswordEmail(String email, String token) {
        String link = emailUtil.buildResetLink(token);
        emailUtil.sendResetPasswordEmail(email, link);
    }
}