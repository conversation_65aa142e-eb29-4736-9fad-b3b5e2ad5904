version: '3.3'

volumes:
  auth_db_data:


networks:
  auth-net:
    external: false

services:
  auth-db:
    image: postgres
    #read_only: true
    environment:
      POSTGRES_USER: auth_db_user
      POSTGRES_PASSWORD: auth_db_password
      POSTGRES_DB: auth_db
    ports:
      - "5433:5432"
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U auth_db_user" ]
      interval: 30s
      timeout: 30s
      retries: 3
    restart: on-failure
    deploy:
      restart_policy:
        condition: on-failure
    stdin_open: true
    tty: true
    networks:
      - auth-net
    volumes:
      - auth_db_data:/var/lib/postgresql
#    tmpfs:
#      - /tmp
#      - /var/run/postgresql
#      - /var/lib/postgresql/data/pg_stat_tmp