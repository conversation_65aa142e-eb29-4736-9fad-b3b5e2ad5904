package com.chidhagni.auth.config;

import com.chidhagni.auth.util.EmailUtil;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.javamail.JavaMailSender;
import org.mockito.Mockito;

@TestConfiguration
public class IntegrationTestConfig {

    @Bean
    @Primary
    public JavaMailSender javaMailSender() {
        return Mockito.mock(JavaMailSender.class);
    }

    @Bean
    @Primary
    public EmailUtil emailUtil() {
        EmailUtil mockEmailUtil = Mockito.mock(EmailUtil.class);
        // Mock email methods to prevent real email sending
        Mockito.doNothing().when(mockEmailUtil).sendVerificationEmail(Mockito.anyString(), Mockito.anyString());
        Mockito.doNothing().when(mockEmailUtil).sendResetPasswordEmail(Mockito.anyString(), Mockito.anyString());
        Mockito.when(mockEmailUtil.buildVerificationLink(Mockito.anyString())).thenReturn("http://localhost:8080/verify?token=test-token");
        Mockito.when(mockEmailUtil.buildResetLink(Mockito.anyString())).thenReturn("http://localhost:8080/reset?token=test-token");
        return mockEmailUtil;
    }
} 