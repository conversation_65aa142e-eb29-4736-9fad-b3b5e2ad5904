<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="db.changelog-20240626-01-create-users-table" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                CREATE TABLE users (
                    id UUID PRIMARY KEY,
                    email VARCHAR(255) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    name VARCHA<PERSON>(100) NOT NULL,
                    mobile_number VARCHAR(10) NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    email_verified BOOLEAN DEFAULT TRUE,
                    account_locked BOOLEAN DEFAULT FALSE,
                    failed_login_attempts INTEGER DEFAULT 0,
                    last_login_at TIMESTAMP,
                    social_login_provider VARCHAR(50),
                    social_login_provider_id VARCHAR(255),
                    social_login_provider_image_url VARCHAR(255),
                    created_by UUID,
                    updated_by UUID,
                    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                CREATE INDEX idx_users_email ON users(email);
                CREATE INDEX idx_users_mobile_number ON users(mobile_number);
                CREATE INDEX idx_users_name ON users(name);
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog> 